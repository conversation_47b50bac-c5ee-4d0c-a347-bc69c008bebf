{"name": "mcp-dante-web-client", "version": "1.0.0", "description": "Web client for MCP Dante earthquake data server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:client", "build:client": "echo 'Client build complete'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mcp", "earthquake", "dante", "seismic", "ingv"], "author": "MCP Dante Client", "license": "MIT"}