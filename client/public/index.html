<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Dante - Earthquake Data Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected { background-color: #2ecc71; }
        .status-disconnected { background-color: #e74c3c; }
        .status-connecting { background-color: #f39c12; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .tool-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 20px;
        }
        
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .result-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .earthquake-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }
        
        .magnitude-badge {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 20px;
            color: white;
        }
        
        .mag-low { background-color: #2ecc71; }
        .mag-medium { background-color: #f39c12; }
        .mag-high { background-color: #e74c3c; }
        .mag-very-high { background-color: #8e44ad; }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .btn-earthquake {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s;
        }
        
        .btn-earthquake:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79);
            transform: translateY(-1px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-globe-americas"></i> MCP Dante</h1>
                <p class="mb-0">Earthquake Data Explorer powered by INGV</p>
                <div class="mt-3">
                    <span id="connectionStatus" class="status-indicator status-disconnected"></span>
                    <span id="connectionText">Disconnected</span>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="p-4">
                <!-- Connection Controls -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <button id="connectBtn" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plug"></i> Connect to MCP Server
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button id="disconnectBtn" class="btn btn-danger btn-lg w-100" disabled>
                            <i class="fas fa-times"></i> Disconnect
                        </button>
                    </div>
                </div>
                
                <!-- Tools Section -->
                <div id="toolsSection" style="display: none;">
                    <h3><i class="fas fa-tools"></i> Available Tools</h3>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card tool-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-heartbeat fa-2x text-success mb-3"></i>
                                    <h5>API Status</h5>
                                    <button class="btn btn-earthquake" onclick="callTool('dante_status')">
                                        Check Status
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card tool-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-globe fa-2x text-primary mb-3"></i>
                                    <h5>Recent Events</h5>
                                    <button class="btn btn-earthquake" onclick="getRecentEvents()">
                                        Get Events
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card tool-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-map-marker-alt fa-2x text-warning mb-3"></i>
                                    <h5>Italy Region</h5>
                                    <button class="btn btn-earthquake" onclick="getItalyEvents()">
                                        Get Italy Events
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Custom Query Form -->
                    <div class="card tool-card">
                        <div class="card-header">
                            <h5><i class="fas fa-search"></i> Custom Earthquake Query</h5>
                        </div>
                        <div class="card-body">
                            <form id="customQueryForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="minMag" step="0.1" placeholder="Min Magnitude">
                                            <label for="minMag">Min Magnitude</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="maxMag" step="0.1" placeholder="Max Magnitude">
                                            <label for="maxMag">Max Magnitude</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="minLat" step="0.001" placeholder="Min Latitude">
                                            <label for="minLat">Min Latitude</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="maxLat" step="0.001" placeholder="Max Latitude">
                                            <label for="maxLat">Max Latitude</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="minLon" step="0.001" placeholder="Min Longitude">
                                            <label for="minLon">Min Longitude</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="maxLon" step="0.001" placeholder="Max Longitude">
                                            <label for="maxLon">Max Longitude</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="limit" placeholder="Limit" value="10">
                                            <label for="limit">Limit</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="toolSelect">
                                                <option value="dante_get_events_group">Events Group</option>
                                                <option value="dante_get_events">Events</option>
                                                <option value="dante_get_origins">Origins</option>
                                                <option value="dante_get_magnitudes">Magnitudes</option>
                                            </select>
                                            <label for="toolSelect">Tool</label>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-earthquake btn-lg">
                                    <i class="fas fa-search"></i> Search Earthquakes
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Spinner -->
                <div id="loadingSpinner" class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Querying earthquake data...</p>
                </div>
                
                <!-- Results -->
                <div id="resultsContainer" style="display: none;">
                    <h4><i class="fas fa-chart-line"></i> Results</h4>
                    <div id="results" class="result-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
