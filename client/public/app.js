// MCP Dante Web Client JavaScript

class MCPDanteClient {
    constructor() {
        this.connectionId = null;
        this.isConnected = false;
        this.baseUrl = window.location.origin;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        document.getElementById('connectBtn').addEventListener('click', () => this.connect());
        document.getElementById('disconnectBtn').addEventListener('click', () => this.disconnect());
        document.getElementById('customQueryForm').addEventListener('submit', (e) => this.handleCustomQuery(e));
    }
    
    async connect() {
        this.updateConnectionStatus('connecting', 'Connecting...');
        
        try {
            const response = await fetch(`${this.baseUrl}/api/mcp/connect`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.connectionId = data.connectionId;
                this.isConnected = true;
                this.updateConnectionStatus('connected', 'Connected');
                this.showTools();
                this.showNotification('Successfully connected to MCP server!', 'success');
            } else {
                throw new Error(data.error || 'Connection failed');
            }
        } catch (error) {
            this.updateConnectionStatus('disconnected', 'Connection Failed');
            this.showNotification(`Connection failed: ${error.message}`, 'error');
        }
    }
    
    async disconnect() {
        if (!this.connectionId) return;
        
        try {
            await fetch(`${this.baseUrl}/api/mcp/${this.connectionId}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Error disconnecting:', error);
        }
        
        this.connectionId = null;
        this.isConnected = false;
        this.updateConnectionStatus('disconnected', 'Disconnected');
        this.hideTools();
        this.showNotification('Disconnected from MCP server', 'info');
    }
    
    updateConnectionStatus(status, text) {
        const indicator = document.getElementById('connectionStatus');
        const textElement = document.getElementById('connectionText');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        
        indicator.className = `status-indicator status-${status}`;
        textElement.textContent = text;
        
        if (status === 'connected') {
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
        } else {
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
        }
    }
    
    showTools() {
        document.getElementById('toolsSection').style.display = 'block';
    }
    
    hideTools() {
        document.getElementById('toolsSection').style.display = 'none';
        document.getElementById('resultsContainer').style.display = 'none';
    }
    
    showLoading() {
        document.getElementById('loadingSpinner').style.display = 'block';
        document.getElementById('resultsContainer').style.display = 'none';
    }
    
    hideLoading() {
        document.getElementById('loadingSpinner').style.display = 'none';
    }
    
    async callTool(toolName, args = {}) {
        if (!this.isConnected || !this.connectionId) {
            this.showNotification('Not connected to MCP server', 'error');
            return;
        }
        
        this.showLoading();
        
        try {
            const response = await fetch(`${this.baseUrl}/api/mcp/${this.connectionId}/call`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    toolName: toolName,
                    arguments: args
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.displayResults(data.result, toolName);
                this.showNotification(`${toolName} executed successfully!`, 'success');
            } else {
                throw new Error(data.error || 'Tool call failed');
            }
        } catch (error) {
            this.showNotification(`Error calling ${toolName}: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    displayResults(result, toolName) {
        const resultsContainer = document.getElementById('resultsContainer');
        const resultsDiv = document.getElementById('results');
        
        resultsContainer.style.display = 'block';
        
        if (toolName === 'dante_status') {
            this.displayStatusResults(result, resultsDiv);
        } else {
            this.displayEarthquakeResults(result, resultsDiv, toolName);
        }
    }
    
    displayStatusResults(result, container) {
        const content = result.content?.[0]?.text || JSON.stringify(result);
        
        try {
            const statusData = JSON.parse(content.replace('API Status: ', ''));
            
            container.innerHTML = `
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-server"></i> API Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span class="badge bg-success">${statusData.status}</span></p>
                                <p><strong>Version:</strong> ${statusData.version}</p>
                                <p><strong>Database:</strong> ${statusData['db-connection']}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Database Name:</strong> ${statusData['db-name']}</p>
                                <p><strong>Port:</strong> ${statusData['db-port']}</p>
                                <p><strong>Detail:</strong> ${statusData.detail}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            container.innerHTML = `<pre class="bg-light p-3 rounded">${content}</pre>`;
        }
    }
    
    displayEarthquakeResults(result, container, toolName) {
        const content = result.content?.[0]?.text || JSON.stringify(result);
        
        try {
            // Extract JSON from the response text
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON data found');
            }
            
            const data = JSON.parse(jsonMatch[0]);
            const events = data.data || [];
            
            if (events.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No earthquake data found for the specified criteria.</div>';
                return;
            }
            
            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-list"></i> Found ${events.length} earthquake(s)</h5>
                    <span class="badge bg-primary">${toolName}</span>
                </div>
            `;
            
            events.forEach((event, index) => {
                const magnitude = this.extractMagnitude(event);
                const location = this.extractLocation(event);
                const time = this.extractTime(event);
                const depth = this.extractDepth(event);
                
                html += `
                    <div class="earthquake-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6><i class="fas fa-map-marker-alt"></i> Event #${event.id || index + 1}</h6>
                                <p class="mb-1"><strong>Location:</strong> ${location}</p>
                                <p class="mb-1"><strong>Time:</strong> ${time}</p>
                                <p class="mb-1"><strong>Depth:</strong> ${depth} km</p>
                            </div>
                            <div class="text-end">
                                <span class="magnitude-badge ${this.getMagnitudeClass(magnitude)}">
                                    M ${magnitude}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
        } catch (error) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <h6>Raw Response:</h6>
                    <pre class="mb-0">${content}</pre>
                </div>
            `;
        }
    }
    
    extractMagnitude(event) {
        return event.preferred_magnitude?.mag || 
               event.magnitude?.mag || 
               event.mag || 
               'N/A';
    }
    
    extractLocation(event) {
        const lat = event.preferred_origin?.lat || event.origin?.lat || event.lat;
        const lon = event.preferred_origin?.lon || event.origin?.lon || event.lon;
        const region = event.preferred_origin?.region || event.origin?.region || event.region;
        
        if (region) return region;
        if (lat && lon) return `${parseFloat(lat).toFixed(3)}°, ${parseFloat(lon).toFixed(3)}°`;
        return 'Unknown';
    }
    
    extractTime(event) {
        const time = event.preferred_origin?.ot || event.origin?.ot || event.ot || event.time;
        if (time) {
            return new Date(time).toLocaleString();
        }
        return 'Unknown';
    }
    
    extractDepth(event) {
        return event.preferred_origin?.depth || event.origin?.depth || event.depth || 'N/A';
    }
    
    getMagnitudeClass(magnitude) {
        const mag = parseFloat(magnitude);
        if (isNaN(mag)) return 'mag-low';
        if (mag < 3) return 'mag-low';
        if (mag < 5) return 'mag-medium';
        if (mag < 7) return 'mag-high';
        return 'mag-very-high';
    }
    
    handleCustomQuery(event) {
        event.preventDefault();
        
        const args = {};
        const minMag = document.getElementById('minMag').value;
        const maxMag = document.getElementById('maxMag').value;
        const minLat = document.getElementById('minLat').value;
        const maxLat = document.getElementById('maxLat').value;
        const minLon = document.getElementById('minLon').value;
        const maxLon = document.getElementById('maxLon').value;
        const limit = document.getElementById('limit').value;
        const toolName = document.getElementById('toolSelect').value;
        
        if (minMag) args.minmag = parseFloat(minMag);
        if (maxMag) args.maxmag = parseFloat(maxMag);
        if (minLat) args.minlat = parseFloat(minLat);
        if (maxLat) args.maxlat = parseFloat(maxLat);
        if (minLon) args.minlon = parseFloat(minLon);
        if (maxLon) args.maxlon = parseFloat(maxLon);
        if (limit) args.limit = parseInt(limit);
        
        this.callTool(toolName, args);
    }
    
    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions for quick actions
function callTool(toolName, args = {}) {
    window.mcpClient.callTool(toolName, args);
}

function getRecentEvents() {
    callTool('dante_get_events_group', { minmag: 3.0, limit: 10 });
}

function getItalyEvents() {
    callTool('dante_get_events', {
        minlat: 35.0,
        maxlat: 47.0,
        minlon: 6.0,
        maxlon: 19.0,
        minmag: 2.0,
        limit: 15
    });
}

// Initialize the client when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.mcpClient = new MCPDanteClient();
});
