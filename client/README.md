# MCP Dante Web Client

A modern web application for interacting with the MCP Dante earthquake data server through a user-friendly interface.

## Features

- **Real-time Connection**: Connect to the MCP Dante server with live status indicators
- **Interactive Dashboard**: Beautiful, responsive web interface built with Bootstrap 5
- **Quick Actions**: Pre-configured buttons for common earthquake queries
- **Custom Queries**: Advanced form for custom earthquake data searches
- **Visual Results**: Formatted earthquake data with magnitude color coding
- **Real-time Notifications**: Toast notifications for user feedback

## Architecture

- **Frontend**: Vanilla JavaScript with Bootstrap 5 for responsive UI
- **Backend**: Node.js/Express server that communicates with MCP server
- **MCP Communication**: Spawns and manages MCP server processes via stdio
- **Containerized**: Fully dockerized for easy deployment

## Quick Start

### Using Docker Compose (Recommended)

1. From the project root directory:
```bash
# Build and start both MCP server and web client
docker-compose up mcp-dante-web

# Or run in background
docker-compose up -d mcp-dante-web
```

2. Open your browser and navigate to:
```
http://localhost:3000
```

### Manual Setup

1. Install dependencies:
```bash
cd client
npm install
```

2. Start the server:
```bash
npm start
```

3. Open http://localhost:3000 in your browser

## Usage

### 1. Connect to MCP Server
- Click the "Connect to MCP Server" button
- Wait for the connection status to show "Connected" (green indicator)

### 2. Quick Actions
- **API Status**: Check the health of the Dante API
- **Recent Events**: Get recent earthquakes with magnitude > 3.0
- **Italy Region**: Get earthquakes in the Italy geographic region

### 3. Custom Queries
Use the custom query form to search for earthquakes with specific criteria:
- **Magnitude Range**: Min/Max magnitude filters
- **Geographic Bounds**: Latitude/Longitude bounding box
- **Result Limit**: Maximum number of results to return
- **Tool Selection**: Choose between different MCP tools

### 4. View Results
- Results are displayed in formatted cards with earthquake details
- Magnitude badges are color-coded by severity:
  - 🟢 Green: < 3.0 (Low)
  - 🟡 Yellow: 3.0-5.0 (Medium)
  - 🔴 Red: 5.0-7.0 (High)
  - 🟣 Purple: > 7.0 (Very High)

## Available MCP Tools

The web client provides access to all MCP Dante tools:

1. **dante_status** - Check API health
2. **dante_get_events_group** - Get clustered earthquake events
3. **dante_get_events** - Get events from same instance
4. **dante_get_origins** - Get earthquake origins
5. **dante_get_magnitudes** - Get magnitude data
6. **dante_get_all_data** - Get comprehensive earthquake data

## API Endpoints

The web client exposes the following REST API endpoints:

- `GET /api/status` - Client health check
- `POST /api/mcp/connect` - Connect to MCP server
- `GET /api/mcp/:id/tools` - List available MCP tools
- `POST /api/mcp/:id/call` - Call an MCP tool
- `DELETE /api/mcp/:id` - Disconnect from MCP server

## Configuration

### Environment Variables

- `PORT` - Web server port (default: 3000)
- `NODE_ENV` - Environment mode (development/production)

### Docker Configuration

The client is configured to work seamlessly with the MCP server container:
- MCP server files are mounted as volumes
- Python dependencies are pre-installed
- Health checks ensure proper startup

## Development

### File Structure

```
client/
├── package.json          # Node.js dependencies
├── server.js             # Express server with MCP communication
├── Dockerfile            # Container configuration
├── public/
│   ├── index.html        # Main web interface
│   └── app.js           # Frontend JavaScript
└── README.md            # This file
```

### Local Development

1. Start the MCP server separately:
```bash
cd ../src
python mcp_dante_server.py
```

2. Start the web client in development mode:
```bash
cd client
npm run dev
```

## Troubleshooting

### Connection Issues
- Ensure the MCP server container is running
- Check that Python dependencies are installed
- Verify the MCP server path in server.js

### Port Conflicts
- Change the PORT environment variable if 3000 is in use
- Update docker-compose.yml port mapping if needed

### Python Path Issues
- Ensure Python 3 is available in the container
- Check that MCP dependencies are installed

## Security Notes

- The web client runs on localhost by default
- No authentication is implemented (suitable for development/internal use)
- MCP communication is handled server-side for security

## License

This project follows the same license terms as the main MCP Dante project.
