#!/bin/bash

# MCP Dante Platform Setup Script
# This script helps you set up and run the MCP Dante platform components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed!"
}

# Function to create network
create_network() {
    print_status "Creating Docker network 'mcp-network'..."
    
    if docker network ls | grep -q mcp-network; then
        print_warning "Network 'mcp-network' already exists."
    else
        docker network create mcp-network
        print_success "Network 'mcp-network' created successfully!"
    fi
}

# Function to build images
build_images() {
    print_status "Building Docker images..."
    
    print_status "Building MCP server image..."
    docker build -t mcp-dante-server ./mcp-server
    
    print_status "Building web client image..."
    docker build -t mcp-dante-web-client ./web-client
    
    print_success "All images built successfully!"
}

# Function to start components
start_full_stack() {
    print_status "Starting full stack (MCP server + Web client)..."
    docker-compose --profile full-stack up -d
    print_success "Full stack started successfully!"
    print_status "Web interface available at: http://localhost:3000"
    print_status "Chat interface available at: http://localhost:3000/chat.html"
}

start_mcp_server() {
    print_status "Starting MCP server only..."
    docker-compose --profile mcp-server up -d
    print_success "MCP server started successfully!"
}

start_web_client() {
    print_status "Starting web client only..."
    docker-compose --profile web-client up -d
    print_success "Web client started successfully!"
    print_status "Web interface available at: http://localhost:3000"
    print_status "Chat interface available at: http://localhost:3000/chat.html"
}

# Function to restart components
restart_all() {
    print_status "Rebuilding and restarting all components..."
    docker-compose down
    build_images
    start_full_stack
    print_success "All components restarted!"
}

# Function to stop components
stop_all() {
    print_status "Stopping all components..."
    docker-compose down
    print_success "All components stopped!"
}

# Function to show logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_status "Showing logs for all services..."
        docker-compose logs -f
    else
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    fi
}

# Function to check status
check_status() {
    print_status "Checking component status..."
    
    echo ""
    print_status "Docker containers:"
    docker ps --filter "name=mcp-dante" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    print_status "Testing web client health..."
    if curl -s http://localhost:3000/api/status > /dev/null; then
        print_success "Web client is healthy!"
    else
        print_warning "Web client is not responding on port 3000"
    fi
}

# Function to run tests
run_tests() {
    print_status "Running MCP server tests..."
    docker-compose --profile test up --abort-on-container-exit mcp-dante-test
    print_success "MCP server tests completed!"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    if [ -f "./test_integration.sh" ]; then
        ./test_integration.sh
    else
        print_error "Integration test script not found!"
        exit 1
    fi
}

# Function to clean up
cleanup() {
    print_status "Cleaning up..."
    
    print_status "Stopping containers..."
    docker-compose down
    
    print_status "Removing images..."
    docker rmi -f mcp-dante-server mcp-dante-web-client 2>/dev/null || true
    
    print_status "Removing network..."
    docker network rm mcp-network 2>/dev/null || true
    
    print_success "Cleanup completed!"
}

# Function to show help
show_help() {
    echo "MCP Dante Platform Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup           - Check prerequisites and create network"
    echo "  build           - Build Docker images"
    echo "  start           - Start full stack (MCP server + Web client)"
    echo "  start-mcp       - Start MCP server only"
    echo "  start-web       - Start web client only"
    echo "  restart         - Rebuild and restart all components"
    echo "  stop            - Stop all components"
    echo "  status          - Check component status"
    echo "  logs [service]  - Show logs (optionally for specific service)"
    echo "  test            - Run MCP server tests"
    echo "  test-integration - Run full integration tests"
    echo "  cleanup         - Stop and remove all components"
    echo "  help            - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup && $0 build && $0 start"
    echo "  $0 logs mcp-dante-server"
    echo "  $0 status"
}

# Main script logic
case "${1:-help}" in
    setup)
        check_prerequisites
        create_network
        ;;
    build)
        build_images
        ;;
    start)
        start_full_stack
        ;;
    start-mcp)
        start_mcp_server
        ;;
    start-web)
        start_web_client
        ;;
    restart)
        restart_all
        ;;
    stop)
        stop_all
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs "$2"
        ;;
    test)
        run_tests
        ;;
    test-integration)
        run_integration_tests
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
