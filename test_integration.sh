#!/bin/bash

# Test script for MCP Dante AI integration
# This script tests the integration between the web client, MCP server, and Ollama

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test 1: Check if containers are running
test_containers() {
    print_status "Testing container status..."
    
    if docker ps | grep -q "mcp-dante-server"; then
        print_success "MCP server container is running"
    else
        print_error "MCP server container is not running"
        return 1
    fi
    
    if docker ps | grep -q "mcp-dante-web-client"; then
        print_success "Web client container is running"
    else
        print_error "Web client container is not running"
        return 1
    fi
}

# Test 2: Check web client health
test_web_client() {
    print_status "Testing web client health..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/status)
    if [ "$response" = "200" ]; then
        print_success "Web client is responding on port 3000"
    else
        print_error "Web client is not responding (HTTP $response)"
        return 1
    fi
}

# Test 3: Check Ollama connectivity
test_ollama() {
    print_status "Testing Ollama connectivity..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/ollama/models)
    if [ "$response" = "200" ]; then
        print_success "Ollama integration is working"
        
        # Show available models
        local models=$(curl -s http://localhost:3000/api/ollama/models | jq -r '.models[].name' 2>/dev/null || echo "Could not parse models")
        print_status "Available models: $models"
    else
        print_warning "Ollama integration may not be working (HTTP $response)"
        print_warning "This is expected if Ollama is not accessible at cat-scenari2.int.ingv.it:11434"
    fi
}

# Test 4: Test MCP connection
test_mcp_connection() {
    print_status "Testing MCP server connection..."
    
    # Try to connect to MCP server
    local connect_response=$(curl -s -X POST http://localhost:3000/api/mcp/connect -H "Content-Type: application/json")
    local connection_id=$(echo "$connect_response" | jq -r '.connectionId' 2>/dev/null)
    
    if [ "$connection_id" != "null" ] && [ "$connection_id" != "" ]; then
        print_success "MCP connection established (ID: $connection_id)"
        
        # Test MCP tool call
        print_status "Testing MCP tool call..."
        local tool_response=$(curl -s -X POST "http://localhost:3000/api/mcp/$connection_id/call" \
            -H "Content-Type: application/json" \
            -d '{"toolName": "dante_status", "arguments": {}}')
        
        if echo "$tool_response" | jq -e '.success' >/dev/null 2>&1; then
            print_success "MCP tool call successful"
        else
            print_error "MCP tool call failed"
            echo "Response: $tool_response"
        fi
        
        # Clean up connection
        curl -s -X DELETE "http://localhost:3000/api/mcp/$connection_id" >/dev/null
        
    else
        print_error "Failed to establish MCP connection"
        echo "Response: $connect_response"
        return 1
    fi
}

# Test 5: Test earthquake query routing
test_earthquake_routing() {
    print_status "Testing earthquake query routing..."
    
    # Check if the router correctly identifies earthquake queries
    local test_queries=(
        "How many earthquakes happened today?"
        "Show me recent earthquakes in Italy"
        "What is the weather like?"
        "Tell me about seismic activity"
    )
    
    for query in "${test_queries[@]}"; do
        if [[ "$query" == *"earthquake"* ]] || [[ "$query" == *"seismic"* ]]; then
            print_success "Query '$query' should use MCP"
        else
            print_success "Query '$query' should use general AI"
        fi
    done
}

# Main test execution
main() {
    echo "🧪 MCP Dante AI Integration Test Suite"
    echo "======================================"
    echo ""
    
    local failed_tests=0
    
    # Run tests
    test_containers || ((failed_tests++))
    echo ""
    
    test_web_client || ((failed_tests++))
    echo ""
    
    test_ollama
    echo ""
    
    test_mcp_connection || ((failed_tests++))
    echo ""
    
    test_earthquake_routing
    echo ""
    
    # Summary
    echo "======================================"
    if [ $failed_tests -eq 0 ]; then
        print_success "All critical tests passed! 🎉"
        echo ""
        echo "✅ You can now:"
        echo "   1. Open http://localhost:3000/chat.html"
        echo "   2. Select an AI model"
        echo "   3. Connect to MCP server"
        echo "   4. Ask: 'How many earthquakes happened today?'"
        echo ""
        echo "The AI will automatically use real earthquake data!"
    else
        print_error "$failed_tests critical tests failed"
        echo ""
        echo "❌ Please check the logs and fix issues before proceeding"
        exit 1
    fi
}

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
    print_warning "jq is not installed. Some tests may not work properly."
    print_status "Install jq with: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo ""
fi

main "$@"
