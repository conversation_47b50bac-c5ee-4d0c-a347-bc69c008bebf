# MCP Dante - Earthquake Data Analysis Platform

A comprehensive platform consisting of two standalone components for AI-powered earthquake data analysis using the Model Context Protocol (MCP) and Ollama integration.

## 🏗️ Architecture Overview

This project is restructured into two independent components:

### 1. **MCP Server Component** (`mcp-server/`)
A standalone MCP server that provides tools to interact with INGV's Dante earthquake data API.

### 2. **Web Chat Client Component** (`web-client/`)
A standalone web application with AI-powered chat interface that connects to both Ollama and the MCP server.

```
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Dante Platform                           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐    ┌─────────────┐ │
│  │   Web Browser   │◄──►│  Web Client      │◄──►│  Ollama API │ │
│  │                 │    │  (Node.js +      │    │ (AI Models) │ │
│  │  - Chat UI      │    │   Socket.IO)     │    └─────────────┘ │
│  │  - MCP Tools    │    │                  │                    │
│  └─────────────────┘    │                  │    ┌─────────────┐ │
│                         │                  │◄──►│ MCP Server  │ │
│                         │                  │    │ (Earthquake │ │
│                         └──────────────────┘    │  Data API)  │ │
│                                                 └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Option 1: Run Both Components Together

```bash
# Create the shared network
docker network create mcp-network

# Start both components
docker-compose --profile full-stack up -d

# Access the web interface
open http://localhost:3000
```

### Option 2: Run Components Separately

#### Start MCP Server Only
```bash
# Create network
docker network create mcp-network

# Start MCP server
docker-compose --profile mcp-server up -d
```

#### Start Web Client Only
```bash
# Ensure MCP server is running first
docker-compose --profile web-client up -d

# Access the web interface
open http://localhost:3000
```

### Option 3: Use Individual Component Directories

#### MCP Server
```bash
cd mcp-server
docker-compose up -d
```

#### Web Client
```bash
cd web-client
docker-compose up -d
```

## 📋 Component Details

### MCP Server Component

**Location**: `mcp-server/`

**Features**:
- GET-only routes for safe earthquake data access
- Comprehensive earthquake data tools
- Built-in request logging
- MCP protocol compliance
- Docker containerization

**Available Tools**:
- `dante_status` - Check API status
- `dante_get_events_group` - Get clustered earthquake events
- `dante_get_events` - Get earthquake events with preferred data
- `dante_get_origins` - Get earthquake origins data
- `dante_get_magnitudes` - Get earthquake magnitude data
- `dante_get_event` - Get full event data by ID
- `dante_get_all_data` - Get comprehensive earthquake data

### Web Chat Client Component

**Location**: `web-client/`

**Features**:
- AI-powered chat interface via Ollama
- Real-time WebSocket communication
- MCP server integration
- Model selection and management
- Interactive earthquake data tools
- Custom query builder
- Responsive Bootstrap UI

**Interfaces**:
- **Main Interface** (`/`): Original MCP tool interface
- **Chat Interface** (`/chat.html`): AI-powered chat with MCP integration

## 🔧 Configuration

### Environment Variables

#### MCP Server
- `PYTHONPATH=/app`
- `PYTHONUNBUFFERED=1`

#### Web Client
- `OLLAMA_URL=http://cat-scenari2.int.ingv.it:11434`
- `MCP_SERVER_HOST=mcp-dante-server`
- `PORT=3000`
- `NODE_ENV=production`

### Network Requirements

- **Shared Network**: `mcp-network` for inter-component communication
- **Ollama Access**: Web client needs access to Ollama instance
- **Docker Socket**: Web client needs Docker socket access for MCP communication

## 🧪 Testing

### Test MCP Server
```bash
docker-compose --profile test up mcp-dante-test
```

### Test Full Stack
```bash
# Start all components
docker-compose --profile full-stack up -d

# Check status
curl http://localhost:3000/api/status
```

## 📖 Usage Examples

### Using the Chat Interface

1. Open http://localhost:3000/chat.html
2. Select an AI model from the dropdown
3. Connect to the MCP server
4. Start asking questions about earthquakes:
   - "Show me recent earthquakes in Italy"
   - "What's the status of the earthquake monitoring system?"
   - "Find earthquakes with magnitude greater than 4.0"

### Using MCP Tools Directly

1. Open http://localhost:3000
2. Connect to MCP server
3. Use quick action buttons or custom queries
4. View earthquake data in formatted tables

### API Integration

```javascript
// Connect to MCP server
const response = await fetch('/api/mcp/connect', { method: 'POST' });
const { connectionId } = await response.json();

// Call earthquake data tool
const result = await fetch(`/api/mcp/${connectionId}/call`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        toolName: 'dante_get_events_group',
        arguments: { minmag: 3.0, limit: 10 }
    })
});
```

## 🔍 Monitoring and Logs

### View Logs
```bash
# All components
docker-compose logs -f

# Specific component
docker-compose logs -f mcp-dante-server
docker-compose logs -f mcp-dante-web-client
```

### Health Checks
```bash
# Web client health
curl http://localhost:3000/api/status

# MCP server status (via web client)
curl http://localhost:3000/api/mcp/{connectionId}/call \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"toolName": "dante_status", "arguments": {}}'
```

## 🛠️ Development

### Local Development Setup

#### MCP Server
```bash
cd mcp-server
pip install -r requirements.txt
python src/mcp_dante_server.py
```

#### Web Client
```bash
cd web-client
npm install
npm run dev
```

### Building Images
```bash
# Build MCP server
docker build -t mcp-dante-server ./mcp-server

# Build web client
docker build -t mcp-dante-web-client ./web-client
```

## 🔒 Security Considerations

- MCP server only implements GET routes for data safety
- Web client uses Docker socket with appropriate permissions
- Ollama integration uses external service URL
- No authentication required for earthquake data (public API)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes to the appropriate component
4. Test both components work together
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Troubleshooting

### Common Issues

1. **Network connectivity**: Ensure `mcp-network` exists: `docker network create mcp-network`
2. **Ollama connection**: Verify Ollama is accessible at configured URL
3. **MCP server communication**: Check Docker socket permissions
4. **Port conflicts**: Ensure port 3000 is available

### Getting Help

- Check component-specific README files in `mcp-server/` and `web-client/`
- Review Docker logs for error messages
- Verify environment variables are set correctly
