#!/usr/bin/env python3
"""
Simple test script for the MCP Dante server
"""

import subprocess
import sys
import time


def test_mcp_server_import():
    """Test that the MCP server can be imported and started"""
    print("=== Testing MCP Dante Server ===\n")

    try:
        # Test importing the server
        print("🔍 Testing server import...")
        result = subprocess.run([
            "python", "-c",
            "import sys; sys.path.append('src'); import mcp_dante_server; print('✅ Server import successful!')"
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            print(result.stdout.strip())
        else:
            print(f"❌ Server import failed: {result.stderr}")
            return False

        # Test that the server can list tools
        print("\n🔍 Testing server tool listing...")
        result = subprocess.run([
            "python", "-c",
            """
import sys; sys.path.append('src')
import asyncio
from mcp_dante_server import handle_list_tools

async def test():
    tools = await handle_list_tools()
    print(f'✅ Found {len(tools)} tools:')
    for tool in tools:
        print(f'  - {tool.name}')

asyncio.run(test())
"""
        ], capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            print(result.stdout.strip())
        else:
            print(f"❌ Tool listing failed: {result.stderr}")
            return False

        print("\n🎉 MCP Server basic tests passed!")
        return True

    except Exception as e:
        print(f"❌ MCP Server test failed: {e}")
        return False


def main():
    """Run basic tests"""
    print("=== MCP Dante Server Basic Test ===\n")

    success = test_mcp_server_import()

    print(f"\n=== Test Results ===")
    print(f"MCP Server: {'✅ PASS' if success else '❌ FAIL'}")

    if success:
        print("\n🎉 MCP Server is ready to use!")
        print("\nTo run the server:")
        print("  docker-compose up mcp-dante")
    else:
        print("\n⚠️  MCP Server has issues")
        sys.exit(1)


if __name__ == "__main__":
    main()
