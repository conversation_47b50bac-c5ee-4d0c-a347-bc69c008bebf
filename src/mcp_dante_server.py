#!/usr/bin/env python3
"""
MCP Server for Dante Earthquake API
Provides tools to interact with INGV's Dante earthquake data API
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional
from datetime import datetime

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
)

from dante_client import DanteAPIClient


# Set up logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Global client instance
dante_client: Optional[DanteAPIClient] = None


def get_client() -> DanteAPIClient:
    """Get or create the Dante API client"""
    global dante_client
    if dante_client is None:
        dante_client = DanteAPIClient()
        logger.info("🔧 Dante API client initialized")
    return dante_client


# Create the MCP server
server = Server("dante-earthquake-api")
logger.info("🚀 MCP Dante Server initialized")


@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools for the Dante API"""
    return [
        Tool(
            name="dante_login",
            description="⚠️ AUTHENTICATION (POST route - not GET): Authenticate with the Dante API to get access token",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "User name"},
                    "email": {"type": "string", "format": "email", "description": "User email"},
                    "password": {"type": "string", "description": "User password"}
                },
                "required": ["name", "email", "password"]
            }
        ),
        Tool(
            name="dante_status",
            description="✅ GET /status - Check the status of the Dante API",
            inputSchema={
                "type": "object",
                "properties": {},
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_events_group",
            description="✅ GET /quakedb/v1/events-group - Get clustered earthquake events with preferred origin and magnitude",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_events",
            description="✅ GET /quakedb/v1/events - Get earthquake events with preferred origin and magnitude from same instance",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_origins",
            description="✅ GET /quakedb/v1/origins - Get earthquake origins data",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_magnitudes",
            description="✅ GET /quakedb/v1/magnitudes - Get earthquake magnitude data",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_event",
            description="✅ GET /quakedb/v1/event - Get full event data (event, origins, magnitudes, arrivals, etc.) by eventid or originid",
            inputSchema={
                "type": "object",
                "properties": {
                    "eventid": {"type": "integer", "description": "Event ID to retrieve"},
                    "originid": {"type": "integer", "description": "Origin ID to retrieve"},
                    "includeallorigins": {"type": "boolean", "description": "Include all origins associated with event"},
                    "includeallmagnitudes": {"type": "boolean", "description": "Include all magnitudes associated with event"},
                    "includeallstationsmagnitudes": {"type": "boolean", "description": "Include all station magnitudes"},
                    "includearrivals": {"type": "boolean", "description": "Include arrival information"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_all_data",
            description="✅ GET /quakedb/v1/all - Get comprehensive earthquake data (origins with all magnitudes)",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls for the Dante API"""

    start_time = time.time()
    logger.info(f"🔧 MCP Tool Called: {name}")
    logger.info(f"📝 Arguments: {json.dumps({k: v for k, v in arguments.items() if k not in ['password']}, default=str)}")

    try:
        client = get_client()

        if name == "dante_login":
            logger.warning("⚠️  LOGIN TOOL: This uses POST method, not GET!")
            result = client.login(
                name=arguments["name"],
                email=arguments["email"],
                password=arguments["password"]
            )
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Authentication successful. Token obtained: {result.get('token', 'N/A')[:20]}..."
            )]
        
        elif name == "dante_status":
            logger.info("✅ GET TOOL: dante_status")
            result = client.get_status()
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"API Status: {json.dumps(result, indent=2)}"
            )]

        elif name == "dante_get_events_group":
            logger.info("✅ GET TOOL: dante_get_events_group")
            # Filter out None values
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_events_group(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Events Group Data: {json.dumps(result, indent=2)}"
            )]

        elif name == "dante_get_events":
            logger.info("✅ GET TOOL: dante_get_events")
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_events(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Events Data: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_origins":
            logger.info("✅ GET TOOL: dante_get_origins")
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_origins(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Origins Data: {json.dumps(result, indent=2)}"
            )]

        elif name == "dante_get_magnitudes":
            logger.info("✅ GET TOOL: dante_get_magnitudes")
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_magnitudes(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Magnitudes Data: {json.dumps(result, indent=2)}"
            )]

        elif name == "dante_get_event":
            logger.info("✅ GET TOOL: dante_get_event")
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_event(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"Event Data: {json.dumps(result, indent=2)}"
            )]

        elif name == "dante_get_all_data":
            logger.info("✅ GET TOOL: dante_get_all_data")
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_all_data(**params)
            duration = time.time() - start_time
            logger.info(f"✅ Tool {name} completed in {duration:.2f}s")
            return [TextContent(
                type="text",
                text=f"All Earthquake Data: {json.dumps(result, indent=2)}"
            )]
        
        else:
            logger.error(f"❌ Unknown tool: {name}")
            return [TextContent(
                type="text",
                text=f"Unknown tool: {name}"
            )]

    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"❌ Tool {name} failed after {duration:.2f}s: {str(e)}")
        return [TextContent(
            type="text",
            text=f"Error calling {name}: {str(e)}"
        )]


async def main():
    """Main entry point for the MCP server"""
    logger.info("🎯 Starting MCP Dante Server...")
    logger.info("📋 Available GET routes:")
    logger.info("  ✅ /status")
    logger.info("  ✅ /quakedb/v1/events-group")
    logger.info("  ✅ /quakedb/v1/events")
    logger.info("  ✅ /quakedb/v1/event")
    logger.info("  ✅ /quakedb/v1/origins")
    logger.info("  ✅ /quakedb/v1/magnitudes")
    logger.info("  ✅ /quakedb/v1/all")
    logger.info("⚠️  Excluded routes (non-GET):")
    logger.info("  ❌ /login (POST)")
    logger.info("  ❌ All POST, PATCH, PUT routes excluded as requested")

    async with stdio_server() as (read_stream, write_stream):
        logger.info("🔗 MCP Server connected via stdio")
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )


if __name__ == "__main__":
    asyncio.run(main())
