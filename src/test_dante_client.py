#!/usr/bin/env python3
"""
Test script for the Dante API client
"""

import sys
import json
from dante_client import DanteAPIClient


def test_api_status():
    """Test the API status endpoint"""
    print("Testing Dante API status...")
    
    try:
        with DanteAPIClient() as client:
            status = client.get_status()
            print("✅ API Status check successful!")
            print(json.dumps(status, indent=2))
            return True
    except Exception as e:
        print(f"❌ API Status check failed: {e}")
        return False


def test_events_without_auth():
    """Test getting events without authentication"""
    print("\nTesting events endpoint without authentication...")
    
    try:
        with DanteAPIClient() as client:
            # Try to get recent events with basic parameters
            events = client.get_events_group(
                limit=5,
                minmag=4.0
            )
            print("✅ Events retrieval successful!")
            print(f"Retrieved {len(events.get('data', []))} events")
            return True
    except Exception as e:
        print(f"❌ Events retrieval failed: {e}")
        print("This might be expected if authentication is required")
        return False


def main():
    """Run basic tests"""
    print("=== Dante API Client Test ===\n")
    
    # Test API status
    status_ok = test_api_status()
    
    # Test events endpoint
    events_ok = test_events_without_auth()
    
    print(f"\n=== Test Results ===")
    print(f"API Status: {'✅ PASS' if status_ok else '❌ FAIL'}")
    print(f"Events Endpoint: {'✅ PASS' if events_ok else '❌ FAIL'}")
    
    if status_ok:
        print("\n🎉 Basic connectivity to Dante API is working!")
    else:
        print("\n⚠️  Could not connect to Dante API")
        sys.exit(1)


if __name__ == "__main__":
    main()
