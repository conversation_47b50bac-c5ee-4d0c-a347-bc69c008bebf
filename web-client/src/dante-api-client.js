// Direct Dante API client for web client
const axios = require('axios');

class DanteAPIClient {
    constructor() {
        this.baseUrl = 'https://caravel-dante.int.ingv.it';
        this.timeout = 30000;
    }

    async makeRequest(endpoint, params = {}) {
        try {
            console.log(`🌐 Making request to Dante API: ${endpoint}`);
            console.log(`📋 Parameters:`, params);

            const response = await axios.get(`${this.baseUrl}${endpoint}`, {
                params,
                timeout: this.timeout,
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'MCP-Dante-Client/1.0'
                }
            });

            console.log(`✅ Dante API response received (${response.status})`);
            return response.data;
        } catch (error) {
            console.error(`❌ Dante API request failed:`, error.message);
            throw new Error(`Dante API error: ${error.message}`);
        }
    }

    async getStatus() {
        return await this.makeRequest('/status');
    }

    async getEventsGroup(params = {}) {
        const cleanParams = this.cleanParams(params);
        return await this.makeRequest('/quakedb/v1/events-group', cleanParams);
    }

    async getEvents(params = {}) {
        const cleanParams = this.cleanParams(params);
        return await this.makeRequest('/quakedb/v1/events', cleanParams);
    }

    async getOrigins(params = {}) {
        const cleanParams = this.cleanParams(params);
        return await this.makeRequest('/quakedb/v1/origins', cleanParams);
    }

    async getMagnitudes(params = {}) {
        const cleanParams = this.cleanParams(params);
        return await this.makeRequest('/quakedb/v1/magnitudes', cleanParams);
    }

    async getAllData(params = {}) {
        const cleanParams = this.cleanParams(params);
        return await this.makeRequest('/quakedb/v1/all', cleanParams);
    }

    cleanParams(params) {
        // Remove null/undefined values and ensure proper types
        const cleaned = {};
        
        for (const [key, value] of Object.entries(params)) {
            if (value !== null && value !== undefined && value !== '') {
                cleaned[key] = value;
            }
        }

        return cleaned;
    }
}

module.exports = DanteAPIClient;
