// Simple HTTP-based MCP client as fallback
const axios = require('axios');

class SimpleMCPClient {
    constructor() {
        this.isConnected = false;
        this.baseUrl = `http://${process.env.MCP_SERVER_HOST || 'mcp-dante-server'}:8000`;
    }

    async connect() {
        try {
            // Test connection to MCP server
            const response = await axios.get(`${this.baseUrl}/health`, { timeout: 5000 });
            this.isConnected = true;
            console.log('✅ Simple MCP client connected via HTTP');
            return true;
        } catch (error) {
            console.log('❌ Simple MCP client connection failed:', error.message);
            this.isConnected = false;
            throw error;
        }
    }

    async listTools() {
        if (!this.isConnected) {
            throw new Error('Not connected to MCP server');
        }

        // Return predefined tools since we know what the MCP server provides
        return {
            tools: [
                { name: 'dante_status', description: 'Check API status' },
                { name: 'dante_get_events_group', description: 'Get clustered earthquake events' },
                { name: 'dante_get_events', description: 'Get earthquake events' },
                { name: 'dante_get_origins', description: 'Get earthquake origins' },
                { name: 'dante_get_magnitudes', description: 'Get earthquake magnitudes' },
                { name: 'dante_get_event', description: 'Get full event data' },
                { name: 'dante_get_all_data', description: 'Get comprehensive earthquake data' }
            ]
        };
    }

    async callTool(name, args) {
        if (!this.isConnected) {
            throw new Error('Not connected to MCP server');
        }

        try {
            // Make HTTP request to MCP server endpoint
            const response = await axios.post(`${this.baseUrl}/tools/${name}`, args, {
                timeout: 30000,
                headers: { 'Content-Type': 'application/json' }
            });

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(response.data, null, 2)
                }]
            };
        } catch (error) {
            console.error(`Error calling MCP tool ${name}:`, error.message);
            throw error;
        }
    }

    disconnect() {
        this.isConnected = false;
        console.log('🔌 Simple MCP client disconnected');
    }
}

module.exports = SimpleMCPClient;
