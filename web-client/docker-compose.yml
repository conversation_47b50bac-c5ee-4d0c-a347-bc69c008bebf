version: '3.8'

services:
  mcp-dante-web-client:
    build: .
    container_name: mcp-dante-web-client
    environment:
      - NODE_ENV=production
      - OLLAMA_URL=http://cat-scenari2.int.ingv.it:11434
      - MCP_SERVER_HOST=mcp-dante-server
    ports:
      - "3000:3000"
    volumes:
      # Mount Docker socket to communicate with MCP server container
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount source code for development
      - ./public:/app/public:ro
      - ./src:/app/src:ro
    restart: unless-stopped
    networks:
      - mcp-network
    depends_on:
      - mcp-dante-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Reference to external MCP server (should be started separately)
  mcp-dante-server:
    image: mcp-dante-server:latest
    container_name: mcp-dante-server
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    stdin_open: true
    tty: true
    command: ["python", "src/mcp_dante_server.py"]
    restart: unless-stopped
    networks:
      - mcp-network
    profiles:
      - with-mcp-server

networks:
  mcp-network:
    driver: bridge
    external: true
