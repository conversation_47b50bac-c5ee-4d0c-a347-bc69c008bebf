{"name": "mcp-dante-web-client", "version": "1.0.0", "description": "Web chat client with MCP Dante earthquake data integration and Ollama support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:client", "build:client": "echo 'Client build complete'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2", "socket.io": "^4.7.2", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mcp", "earthquake", "dante", "seismic", "ingv", "ollama", "chat", "ai"], "author": "MCP Dante Web Client", "license": "MIT"}