const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const path = require('path');
const { Server } = require('socket.io');
const http = require('http');
const axios = require('axios');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 3000;
const OLLAMA_URL = process.env.OLLAMA_URL || 'http://cat-scenari2.int.ingv.it:11434';
const MCP_SERVER_HOST = process.env.MCP_SERVER_HOST || 'mcp-dante-server';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Store active MCP connections and chat sessions
const mcpConnections = new Map();
const chatSessions = new Map();

// Simple HTTP-based MCP Client for direct communication
class SimpleMCPClient {
    constructor() {
        this.isConnected = false;
        this.baseUrl = 'http://localhost:3000'; // Use web client as proxy
    }

    async connect() {
        try {
            console.log('🔌 Connecting to MCP server via HTTP...');
            this.isConnected = true;
            return true;
        } catch (error) {
            console.error('❌ MCP connection failed:', error);
            this.isConnected = false;
            throw error;
        }
    }

    async listTools() {
        return {
            tools: [
                { name: 'dante_status', description: 'Check API status' },
                { name: 'dante_get_events_group', description: 'Get clustered earthquake events' },
                { name: 'dante_get_events', description: 'Get earthquake events' },
                { name: 'dante_get_origins', description: 'Get earthquake origins' },
                { name: 'dante_get_magnitudes', description: 'Get earthquake magnitudes' },
                { name: 'dante_get_event', description: 'Get full event data' },
                { name: 'dante_get_all_data', description: 'Get comprehensive earthquake data' }
            ]
        };
    }

    async callTool(name, args) {
        try {
            console.log(`🔧 Calling MCP tool: ${name} with args:`, args);

            // Simulate MCP tool call with direct API call to Dante
            const DanteAPIClient = require('./src/dante-api-client');
            const danteClient = new DanteAPIClient();
            let result;

            switch (name) {
                case 'dante_status':
                    result = await danteClient.getStatus();
                    break;
                case 'dante_get_events_group':
                    result = await danteClient.getEventsGroup(args);
                    break;
                case 'dante_get_events':
                    result = await danteClient.getEvents(args);
                    break;
                case 'dante_get_origins':
                    result = await danteClient.getOrigins(args);
                    break;
                case 'dante_get_magnitudes':
                    result = await danteClient.getMagnitudes(args);
                    break;
                case 'dante_get_all_data':
                    result = await danteClient.getAllData(args);
                    break;
                default:
                    throw new Error(`Unknown tool: ${name}`);
            }

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(result, null, 2)
                }]
            };
        } catch (error) {
            console.error(`❌ MCP tool call failed:`, error);
            throw error;
        }
    }

    disconnect() {
        this.isConnected = false;
        console.log('🔌 MCP client disconnected');
    }
}

// Legacy MCP Client class (keeping for compatibility)
class MCPClient {
    constructor() {
        this.process = null;
        this.isConnected = false;
        this.messageId = 0;
        this.pendingRequests = new Map();
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                // Connect to the containerized MCP server via docker network
                const mcpServerPath = '/app/src/mcp_dante_server.py';
                const pythonPath = 'python3';

                // Try direct connection first, fallback to docker exec
                console.log(`🔌 Attempting to connect to MCP server: ${MCP_SERVER_HOST}`);

                // Option 1: Try to spawn MCP server directly (for development)
                if (process.env.NODE_ENV !== 'production') {
                    this.process = spawn('python3', ['../mcp-server/src/mcp_dante_server.py'], {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        cwd: __dirname
                    });
                } else {
                    // Option 2: Use docker exec for production
                    this.process = spawn('docker', [
                        'exec', '-i', MCP_SERVER_HOST,
                        pythonPath, mcpServerPath
                    ], {
                        stdio: ['pipe', 'pipe', 'pipe']
                    });
                }

                this.process.stdout.on('data', (data) => {
                    this.handleMessage(data.toString());
                });

                this.process.stderr.on('data', (data) => {
                    const message = data.toString().trim();
                    // Check if it's actually an error or just logging
                    if (message.includes('ERROR') || message.includes('CRITICAL') || message.includes('Exception') || message.includes('Traceback')) {
                        console.error('🚨 MCP Server Error:', message);
                    } else if (message.includes('INFO') || message.includes('DEBUG') || message.includes('WARNING')) {
                        console.log('📋 MCP Server Log:', message);
                    } else {
                        console.log('📝 MCP Server:', message);
                    }
                });

                this.process.on('close', (code) => {
                    console.log(`MCP server process exited with code ${code}`);
                    this.isConnected = false;
                });

                // Wait a bit for the server to start
                setTimeout(() => {
                    // Initialize the MCP session
                    this.sendMessage({
                        jsonrpc: "2.0",
                        id: this.getNextId(),
                        method: "initialize",
                        params: {
                            protocolVersion: "2024-11-05",
                            capabilities: {
                                tools: {}
                            },
                            clientInfo: {
                                name: "dante-web-client",
                                version: "1.0.0"
                            }
                        }
                    }).then((result) => {
                        // Send initialized notification
                        return this.sendMessage({
                            jsonrpc: "2.0",
                            method: "notifications/initialized"
                        });
                    }).then(() => {
                        this.isConnected = true;
                        resolve();
                    }).catch(reject);
                }, 1000); // Wait 1 second for server startup

            } catch (error) {
                reject(error);
            }
        });
    }

    getNextId() {
        return ++this.messageId;
    }

    sendMessage(message) {
        return new Promise((resolve, reject) => {
            if (!this.process) {
                reject(new Error('MCP process not started'));
                return;
            }

            const messageStr = JSON.stringify(message) + '\n';

            // Handle notifications (no response expected)
            if (!message.id) {
                this.process.stdin.write(messageStr);
                resolve();
                return;
            }

            this.pendingRequests.set(message.id, { resolve, reject });
            this.process.stdin.write(messageStr);
        });
    }

    handleMessage(data) {
        const lines = data.trim().split('\n');

        for (const line of lines) {
            if (!line.trim()) continue;

            try {
                const message = JSON.parse(line);

                if (message.id && this.pendingRequests.has(message.id)) {
                    const { resolve, reject } = this.pendingRequests.get(message.id);
                    this.pendingRequests.delete(message.id);

                    if (message.error) {
                        reject(new Error(message.error.message || 'MCP Error'));
                    } else {
                        resolve(message.result);
                    }
                }
            } catch (error) {
                console.error('Error parsing MCP message:', error);
            }
        }
    }

    async listTools() {
        const message = {
            jsonrpc: "2.0",
            id: this.getNextId(),
            method: "tools/list"
        };

        return this.sendMessage(message);
    }

    async callTool(name, args) {
        const message = {
            jsonrpc: "2.0",
            id: this.getNextId(),
            method: "tools/call",
            params: {
                name: name,
                arguments: args || {}
            }
        };

        return this.sendMessage(message);
    }

    disconnect() {
        if (this.process) {
            this.process.kill();
            this.process = null;
        }
        this.isConnected = false;
        this.pendingRequests.clear();
    }
}

// Ollama integration class
class OllamaClient {
    constructor() {
        this.baseUrl = OLLAMA_URL;
    }

    async listModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            return response.data.models || [];
        } catch (error) {
            console.error('Error listing Ollama models:', error.message);
            return [];
        }
    }

    async generateResponse(model, prompt, context = []) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: model,
                prompt: prompt,
                context: context,
                stream: false
            });
            return response.data;
        } catch (error) {
            console.error('Error generating Ollama response:', error.message);
            throw error;
        }
    }

    async chatCompletion(model, messages) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                model: model,
                messages: messages,
                stream: false
            });
            return response.data;
        } catch (error) {
            console.error('Error with Ollama chat completion:', error.message);
            throw error;
        }
    }
}

const ollamaClient = new OllamaClient();

// Smart MCP Tool Router
class MCPToolRouter {
    constructor() {
        this.earthquakeKeywords = [
            'earthquake', 'earthquakes', 'seismic', 'tremor', 'quake', 'quakes',
            'magnitude', 'richter', 'epicenter', 'fault', 'tectonic',
            'aftershock', 'foreshock', 'tsunami', 'geological', 'geology',
            'today', 'recent', 'latest', 'current', 'now', 'this week',
            'italy', 'italian', 'mediterranean', 'europe', 'ingv'
        ];

        this.locationKeywords = {
            'italy': { minlat: 35.0, maxlat: 47.0, minlon: 6.0, maxlon: 19.0 },
            'italian': { minlat: 35.0, maxlat: 47.0, minlon: 6.0, maxlon: 19.0 },
            'mediterranean': { minlat: 30.0, maxlat: 50.0, minlon: -10.0, maxlon: 40.0 },
            'europe': { minlat: 35.0, maxlat: 70.0, minlon: -10.0, maxlon: 40.0 }
        };
    }

    shouldUseMCP(message) {
        const lowerMessage = message.toLowerCase();
        return this.earthquakeKeywords.some(keyword => lowerMessage.includes(keyword));
    }

    extractParameters(message) {
        const lowerMessage = message.toLowerCase();
        const params = {};

        // Extract time-based parameters
        if (lowerMessage.includes('today')) {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            params.starttime = yesterday.toISOString();
            params.endtime = today.toISOString();
        } else if (lowerMessage.includes('recent') || lowerMessage.includes('latest')) {
            const now = new Date();
            const weekAgo = new Date(now);
            weekAgo.setDate(weekAgo.getDate() - 7);
            params.starttime = weekAgo.toISOString();
            params.endtime = now.toISOString();
        } else if (lowerMessage.includes('this week')) {
            const now = new Date();
            const weekAgo = new Date(now);
            weekAgo.setDate(weekAgo.getDate() - 7);
            params.starttime = weekAgo.toISOString();
            params.endtime = now.toISOString();
        }

        // Extract magnitude parameters
        const magMatch = lowerMessage.match(/magnitude\s*(?:greater\s*than|above|over|\>)\s*(\d+(?:\.\d+)?)/);
        if (magMatch) {
            params.minmag = parseFloat(magMatch[1]);
        }

        const magRangeMatch = lowerMessage.match(/magnitude\s*(\d+(?:\.\d+)?)\s*(?:to|-)\s*(\d+(?:\.\d+)?)/);
        if (magRangeMatch) {
            params.minmag = parseFloat(magRangeMatch[1]);
            params.maxmag = parseFloat(magRangeMatch[2]);
        }

        // Extract location parameters
        for (const [location, coords] of Object.entries(this.locationKeywords)) {
            if (lowerMessage.includes(location)) {
                Object.assign(params, coords);
                break;
            }
        }

        // Set default limit
        params.limit = 20;

        return params;
    }

    selectTool(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('status') || lowerMessage.includes('system') || lowerMessage.includes('api')) {
            return 'dante_status';
        } else if (lowerMessage.includes('detailed') || lowerMessage.includes('full') || lowerMessage.includes('complete')) {
            return 'dante_get_all_data';
        } else if (lowerMessage.includes('origin') || lowerMessage.includes('location')) {
            return 'dante_get_origins';
        } else if (lowerMessage.includes('magnitude')) {
            return 'dante_get_magnitudes';
        } else {
            return 'dante_get_events_group'; // Default for general earthquake queries
        }
    }

    async callMCPTool(mcpClient, message) {
        if (!this.shouldUseMCP(message)) {
            return null;
        }

        const toolName = this.selectTool(message);
        const params = this.extractParameters(message);

        try {
            const result = await mcpClient.callTool(toolName, params);
            return {
                toolName,
                params,
                result
            };
        } catch (error) {
            console.error('Error calling MCP tool:', error);
            return {
                toolName,
                params,
                error: error.message
            };
        }
    }
}

const mcpRouter = new MCPToolRouter();

// API Routes
app.get('/api/status', (req, res) => {
    res.json({
        status: 'ok',
        message: 'MCP Dante Web Client with Ollama integration is running',
        timestamp: new Date().toISOString(),
        ollama_url: OLLAMA_URL,
        mcp_server_host: MCP_SERVER_HOST
    });
});

// Debug route for testing MCP routing
app.post('/api/debug/test-routing', (req, res) => {
    const { message } = req.body;
    const shouldUse = mcpRouter.shouldUseMCP(message);
    const toolName = mcpRouter.selectTool(message);
    const params = mcpRouter.extractParameters(message);

    res.json({
        message,
        shouldUseMCP: shouldUse,
        selectedTool: toolName,
        extractedParams: params,
        availableConnections: Array.from(mcpConnections.keys()),
        chatSessions: Array.from(chatSessions.keys())
    });
});

// Ollama routes
app.get('/api/ollama/models', async (req, res) => {
    try {
        const models = await ollamaClient.listModels();
        res.json({ success: true, models });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/ollama/chat', async (req, res) => {
    try {
        const { model, messages } = req.body;
        const response = await ollamaClient.chatCompletion(model, messages);
        res.json({ success: true, response });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/mcp/connect', async (req, res) => {
    try {
        console.log('🔌 Attempting to connect to MCP server...');
        const client = new SimpleMCPClient();
        await client.connect();

        const connectionId = Date.now().toString();
        mcpConnections.set(connectionId, client);

        console.log(`✅ MCP connection established with ID: ${connectionId}`);
        console.log(`📊 Total MCP connections: ${mcpConnections.size}`);

        res.json({
            success: true,
            connectionId: connectionId,
            message: 'Connected to MCP server (Direct API)'
        });
    } catch (error) {
        console.error('❌ MCP connection failed:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/api/mcp/:connectionId/tools', async (req, res) => {
    try {
        const { connectionId } = req.params;
        const client = mcpConnections.get(connectionId);

        if (!client || !client.isConnected) {
            return res.status(404).json({
                success: false,
                error: 'MCP connection not found or disconnected'
            });
        }

        const tools = await client.listTools();
        res.json({ success: true, tools: tools });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/mcp/:connectionId/call', async (req, res) => {
    try {
        const { connectionId } = req.params;
        const { toolName, arguments: toolArgs } = req.body;

        const client = mcpConnections.get(connectionId);

        if (!client || !client.isConnected) {
            return res.status(404).json({
                success: false,
                error: 'MCP connection not found or disconnected'
            });
        }

        const result = await client.callTool(toolName, toolArgs);
        res.json({ success: true, result: result });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.delete('/api/mcp/:connectionId', (req, res) => {
    const { connectionId } = req.params;
    const client = mcpConnections.get(connectionId);

    if (client) {
        client.disconnect();
        mcpConnections.delete(connectionId);
    }

    res.json({
        success: true,
        message: 'MCP connection closed'
    });
});

// Serve the main HTML page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO for real-time chat
io.on('connection', (socket) => {
    console.log('🔗 User connected:', socket.id);

    socket.on('join-chat', (data) => {
        const { sessionId } = data;
        socket.join(sessionId);

        if (!chatSessions.has(sessionId)) {
            chatSessions.set(sessionId, {
                messages: [],
                mcpConnectionId: null
            });
        }
    });

    socket.on('set-mcp-connection', (data) => {
        const { sessionId, connectionId } = data;
        console.log(`🔗 Linking session ${sessionId} to MCP connection ${connectionId}`);
        const session = chatSessions.get(sessionId);
        if (session) {
            session.mcpConnectionId = connectionId;
            console.log(`✅ Chat session ${sessionId} successfully linked to MCP connection ${connectionId}`);
            console.log(`📋 Session now has:`, {
                sessionId,
                mcpConnectionId: session.mcpConnectionId,
                messageCount: session.messages.length
            });
        } else {
            console.log(`❌ Session ${sessionId} not found when trying to link MCP connection`);
        }
    });

    socket.on('send-message', async (data) => {
        const { sessionId, message, model } = data;
        const session = chatSessions.get(sessionId);

        console.log(`📨 Received message from session ${sessionId}: "${message}"`);
        console.log(`📋 Session data:`, {
            sessionExists: !!session,
            mcpConnectionId: session?.mcpConnectionId,
            messageCount: session?.messages?.length
        });
        console.log(`🔗 Available MCP connections:`, Array.from(mcpConnections.keys()));

        if (!session) {
            console.log('❌ Session not found!');
            return;
        }

        // Add user message to session
        session.messages.push({ role: 'user', content: message });

        try {
            let mcpData = null;
            let enhancedMessage = message;

            // Debug: Test keyword detection
            const shouldUse = mcpRouter.shouldUseMCP(message);
            console.log(`🔍 Keyword detection result for "${message}": ${shouldUse}`);

            // Check if this is an earthquake-related query and we have MCP connection
            if (shouldUse) {
                console.log(`🔍 Detected earthquake-related query: "${message}"`);

                if (session.mcpConnectionId) {
                    console.log(`🔗 Session has MCP connection ID: ${session.mcpConnectionId}`);
                    const mcpClient = mcpConnections.get(session.mcpConnectionId);
                    console.log(`🔧 MCP client status:`, {
                        exists: !!mcpClient,
                        isConnected: mcpClient?.isConnected
                    });

                    if (mcpClient && mcpClient.isConnected) {
                        console.log('🔧 Routing earthquake query through MCP tools...');
                        mcpData = await mcpRouter.callMCPTool(mcpClient, message);
                        console.log('📊 MCP tool result:', mcpData);
                    } else {
                        console.log('⚠️ MCP client not connected, using general AI response');
                    }
                } else {
                    console.log('⚠️ No MCP connection for this session, using general AI response');
                }
            } else {
                console.log(`💬 General query (no MCP needed): "${message}"`);
            }

            if (mcpData && mcpData.result) {
                // Extract earthquake data from MCP result
                const resultText = mcpData.result.content?.[0]?.text || JSON.stringify(mcpData.result);

                // Enhance the message with real earthquake data context
                enhancedMessage = `${message}

REAL-TIME EARTHQUAKE DATA (from INGV Dante API):
Tool used: ${mcpData.toolName}
Parameters: ${JSON.stringify(mcpData.params, null, 2)}
Data: ${resultText}

Please analyze this real earthquake data and provide a comprehensive answer based on the actual data above. If the data shows specific earthquakes, mention details like magnitude, location, time, and depth. Be specific about the numbers and locations found in the data.`;

                // Emit MCP tool usage info to client
                io.to(sessionId).emit('mcp-tool-used', {
                    toolName: mcpData.toolName,
                    params: mcpData.params,
                    timestamp: new Date().toISOString()
                });
            } else if (mcpData && mcpData.error) {
                enhancedMessage = `${message}

Note: I tried to get real-time earthquake data but encountered an error: ${mcpData.error}. I'll provide a general response instead.`;
            }
        }
            }

            // Prepare messages for AI with enhanced context if available
            const messagesForAI = [...session.messages];
    messagesForAI[messagesForAI.length - 1].content = enhancedMessage;

    // Get AI response from Ollama
    const response = await ollamaClient.chatCompletion(model, messagesForAI);
    const aiMessage = response.message.content;

    // Add AI response to session (store original user message, not enhanced)
    session.messages.push({ role: 'assistant', content: aiMessage });

    // Emit response to all clients in the session
    io.to(sessionId).emit('message-response', {
        user: message,
        assistant: aiMessage,
        timestamp: new Date().toISOString(),
        usedMCP: mcpData !== null
    });

} catch (error) {
    console.error('Error in chat message handling:', error);
    io.to(sessionId).emit('error', {
        message: 'Error generating response',
        error: error.message
    });
}
    });

socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
});
});

// Start server
server.listen(PORT, () => {
    console.log(`🌍 MCP Dante Web Client with Ollama integration running on http://localhost:${PORT}`);
    console.log(`🤖 Ollama URL: ${OLLAMA_URL}`);
    console.log(`🔧 MCP Server Host: ${MCP_SERVER_HOST}`);
    console.log(`📊 Ready for AI-powered earthquake data analysis!`);
});

// Cleanup on exit
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down MCP Dante Web Client...');

    // Close all MCP connections
    for (const [id, client] of mcpConnections) {
        client.disconnect();
    }

    process.exit(0);
});
