const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const path = require('path');
const { Server } = require('socket.io');
const http = require('http');
const axios = require('axios');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 3000;
const OLLAMA_URL = process.env.OLLAMA_URL || 'http://cat-scenari2.int.ingv.it:11434';
const MCP_SERVER_HOST = process.env.MCP_SERVER_HOST || 'mcp-dante-server';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Store active MCP connections and chat sessions
const mcpConnections = new Map();
const chatSessions = new Map();

// MCP Client class to handle communication with the MCP server
class MCPClient {
    constructor() {
        this.process = null;
        this.isConnected = false;
        this.messageId = 0;
        this.pendingRequests = new Map();
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                // Connect to the containerized MCP server via docker network
                const mcpServerPath = '/app/src/mcp_dante_server.py';
                const pythonPath = 'python3';

                // Use docker exec to connect to the MCP server container
                this.process = spawn('docker', [
                    'exec', '-i', MCP_SERVER_HOST,
                    pythonPath, mcpServerPath
                ], {
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                this.process.stdout.on('data', (data) => {
                    this.handleMessage(data.toString());
                });

                this.process.stderr.on('data', (data) => {
                    const message = data.toString().trim();
                    // Check if it's actually an error or just logging
                    if (message.includes('ERROR') || message.includes('CRITICAL') || message.includes('Exception') || message.includes('Traceback')) {
                        console.error('🚨 MCP Server Error:', message);
                    } else if (message.includes('INFO') || message.includes('DEBUG') || message.includes('WARNING')) {
                        console.log('📋 MCP Server Log:', message);
                    } else {
                        console.log('📝 MCP Server:', message);
                    }
                });

                this.process.on('close', (code) => {
                    console.log(`MCP server process exited with code ${code}`);
                    this.isConnected = false;
                });

                // Wait a bit for the server to start
                setTimeout(() => {
                    // Initialize the MCP session
                    this.sendMessage({
                        jsonrpc: "2.0",
                        id: this.getNextId(),
                        method: "initialize",
                        params: {
                            protocolVersion: "2024-11-05",
                            capabilities: {
                                tools: {}
                            },
                            clientInfo: {
                                name: "dante-web-client",
                                version: "1.0.0"
                            }
                        }
                    }).then((result) => {
                        // Send initialized notification
                        return this.sendMessage({
                            jsonrpc: "2.0",
                            method: "notifications/initialized"
                        });
                    }).then(() => {
                        this.isConnected = true;
                        resolve();
                    }).catch(reject);
                }, 1000); // Wait 1 second for server startup

            } catch (error) {
                reject(error);
            }
        });
    }

    getNextId() {
        return ++this.messageId;
    }

    sendMessage(message) {
        return new Promise((resolve, reject) => {
            if (!this.process) {
                reject(new Error('MCP process not started'));
                return;
            }

            const messageStr = JSON.stringify(message) + '\n';

            // Handle notifications (no response expected)
            if (!message.id) {
                this.process.stdin.write(messageStr);
                resolve();
                return;
            }

            this.pendingRequests.set(message.id, { resolve, reject });
            this.process.stdin.write(messageStr);
        });
    }

    handleMessage(data) {
        const lines = data.trim().split('\n');

        for (const line of lines) {
            if (!line.trim()) continue;

            try {
                const message = JSON.parse(line);

                if (message.id && this.pendingRequests.has(message.id)) {
                    const { resolve, reject } = this.pendingRequests.get(message.id);
                    this.pendingRequests.delete(message.id);

                    if (message.error) {
                        reject(new Error(message.error.message || 'MCP Error'));
                    } else {
                        resolve(message.result);
                    }
                }
            } catch (error) {
                console.error('Error parsing MCP message:', error);
            }
        }
    }

    async listTools() {
        const message = {
            jsonrpc: "2.0",
            id: this.getNextId(),
            method: "tools/list"
        };

        return this.sendMessage(message);
    }

    async callTool(name, args) {
        const message = {
            jsonrpc: "2.0",
            id: this.getNextId(),
            method: "tools/call",
            params: {
                name: name,
                arguments: args || {}
            }
        };

        return this.sendMessage(message);
    }

    disconnect() {
        if (this.process) {
            this.process.kill();
            this.process = null;
        }
        this.isConnected = false;
        this.pendingRequests.clear();
    }
}

// Ollama integration class
class OllamaClient {
    constructor() {
        this.baseUrl = OLLAMA_URL;
    }

    async listModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            return response.data.models || [];
        } catch (error) {
            console.error('Error listing Ollama models:', error.message);
            return [];
        }
    }

    async generateResponse(model, prompt, context = []) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: model,
                prompt: prompt,
                context: context,
                stream: false
            });
            return response.data;
        } catch (error) {
            console.error('Error generating Ollama response:', error.message);
            throw error;
        }
    }

    async chatCompletion(model, messages) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                model: model,
                messages: messages,
                stream: false
            });
            return response.data;
        } catch (error) {
            console.error('Error with Ollama chat completion:', error.message);
            throw error;
        }
    }
}

const ollamaClient = new OllamaClient();

// API Routes
app.get('/api/status', (req, res) => {
    res.json({
        status: 'ok',
        message: 'MCP Dante Web Client with Ollama integration is running',
        timestamp: new Date().toISOString(),
        ollama_url: OLLAMA_URL,
        mcp_server_host: MCP_SERVER_HOST
    });
});

// Ollama routes
app.get('/api/ollama/models', async (req, res) => {
    try {
        const models = await ollamaClient.listModels();
        res.json({ success: true, models });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/ollama/chat', async (req, res) => {
    try {
        const { model, messages } = req.body;
        const response = await ollamaClient.chatCompletion(model, messages);
        res.json({ success: true, response });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/mcp/connect', async (req, res) => {
    try {
        const client = new MCPClient();
        await client.connect();

        const connectionId = Date.now().toString();
        mcpConnections.set(connectionId, client);

        res.json({
            success: true,
            connectionId: connectionId,
            message: 'Connected to MCP server'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/api/mcp/:connectionId/tools', async (req, res) => {
    try {
        const { connectionId } = req.params;
        const client = mcpConnections.get(connectionId);

        if (!client || !client.isConnected) {
            return res.status(404).json({
                success: false,
                error: 'MCP connection not found or disconnected'
            });
        }

        const tools = await client.listTools();
        res.json({ success: true, tools: tools });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/mcp/:connectionId/call', async (req, res) => {
    try {
        const { connectionId } = req.params;
        const { toolName, arguments: toolArgs } = req.body;

        const client = mcpConnections.get(connectionId);

        if (!client || !client.isConnected) {
            return res.status(404).json({
                success: false,
                error: 'MCP connection not found or disconnected'
            });
        }

        const result = await client.callTool(toolName, toolArgs);
        res.json({ success: true, result: result });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.delete('/api/mcp/:connectionId', (req, res) => {
    const { connectionId } = req.params;
    const client = mcpConnections.get(connectionId);

    if (client) {
        client.disconnect();
        mcpConnections.delete(connectionId);
    }

    res.json({
        success: true,
        message: 'MCP connection closed'
    });
});

// Serve the main HTML page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO for real-time chat
io.on('connection', (socket) => {
    console.log('User connected:', socket.id);

    socket.on('join-chat', (data) => {
        const { sessionId } = data;
        socket.join(sessionId);

        if (!chatSessions.has(sessionId)) {
            chatSessions.set(sessionId, {
                messages: [],
                mcpConnectionId: null
            });
        }
    });

    socket.on('send-message', async (data) => {
        const { sessionId, message, model } = data;
        const session = chatSessions.get(sessionId);

        if (!session) return;

        // Add user message to session
        session.messages.push({ role: 'user', content: message });

        try {
            // Get AI response from Ollama
            const response = await ollamaClient.chatCompletion(model, session.messages);
            const aiMessage = response.message.content;

            // Add AI response to session
            session.messages.push({ role: 'assistant', content: aiMessage });

            // Emit response to all clients in the session
            io.to(sessionId).emit('message-response', {
                user: message,
                assistant: aiMessage,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            io.to(sessionId).emit('error', {
                message: 'Error generating response',
                error: error.message
            });
        }
    });

    socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
    });
});

// Start server
server.listen(PORT, () => {
    console.log(`🌍 MCP Dante Web Client with Ollama integration running on http://localhost:${PORT}`);
    console.log(`🤖 Ollama URL: ${OLLAMA_URL}`);
    console.log(`🔧 MCP Server Host: ${MCP_SERVER_HOST}`);
    console.log(`📊 Ready for AI-powered earthquake data analysis!`);
});

// Cleanup on exit
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down MCP Dante Web Client...');

    // Close all MCP connections
    for (const [id, client] of mcpConnections) {
        client.disconnect();
    }

    process.exit(0);
});
