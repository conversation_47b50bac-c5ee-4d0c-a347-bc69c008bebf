# MCP Dante Web Chat Client

A standalone web application that provides an AI-powered chat interface for earthquake data analysis. This client connects to both a local Ollama instance and the MCP Dante server to provide intelligent earthquake data querying and analysis.

## Features

- **AI Chat Interface**: Real-time chat with AI models via Ollama
- **MCP Integration**: Connect to MCP Dante server for earthquake data tools
- **Real-time Communication**: WebSocket-based chat with Socket.IO
- **Model Selection**: Choose from available Ollama models
- **Interactive Tools**: Quick access to common earthquake data queries
- **Custom Queries**: Build custom earthquake data queries with filters
- **Responsive Design**: Modern Bootstrap-based UI

## Prerequisites

- **Ollama Instance**: Running at `http://cat-scenari2.int.ingv.it:11434`
- **MCP Dante Server**: Available via Docker network
- **Docker**: For containerized deployment

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Start the web client (assumes MCP server is running separately)
docker-compose up -d

# Or start with MCP server included
docker-compose --profile with-mcp-server up -d

# View logs
docker-compose logs -f mcp-dante-web-client

# Stop the client
docker-compose down
```

### Using Docker directly

```bash
# Build the image
docker build -t mcp-dante-web-client .

# Run the container
docker run -d \
  --name mcp-dante-web-client \
  -p 3000:3000 \
  -e OLLAMA_URL=http://cat-scenari2.int.ingv.it:11434 \
  -e MCP_SERVER_HOST=mcp-dante-server \
  -v /var/run/docker.sock:/var/run/docker.sock \
  --network mcp-network \
  mcp-dante-web-client
```

### Local Development

```bash
# Install dependencies
npm install

# Set environment variables
export OLLAMA_URL=http://cat-scenari2.int.ingv.it:11434
export MCP_SERVER_HOST=mcp-dante-server

# Start the development server
npm run dev
```

## Usage

1. **Access the Application**: Open http://localhost:3000 in your browser
2. **Select AI Model**: Choose an available Ollama model from the dropdown
3. **Connect to MCP**: Click "Connect to MCP Server" to enable earthquake data tools
4. **Start Chatting**: Ask questions about earthquakes or use the quick action buttons
5. **Use Tools**: Access earthquake data through the MCP tools panel

## Available Interfaces

- **Main Interface** (`/`): Original MCP tool interface
- **Chat Interface** (`/chat.html`): AI-powered chat with MCP integration

## Configuration

### Environment Variables

- `OLLAMA_URL`: URL of the Ollama instance (default: http://cat-scenari2.int.ingv.it:11434)
- `MCP_SERVER_HOST`: Hostname of the MCP server container (default: mcp-dante-server)
- `PORT`: Port for the web server (default: 3000)
- `NODE_ENV`: Environment mode (development/production)

### Network Configuration

The application requires access to:
- **Ollama API**: For AI model inference
- **MCP Server**: For earthquake data tools
- **Docker Socket**: For MCP server communication (in containerized mode)

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Browser   │◄──►│  Web Client      │◄──►│  Ollama API     │
│                 │    │  (Node.js +      │    │  (AI Models)    │
│  - Chat UI      │    │   Socket.IO)     │    └─────────────────┘
│  - MCP Tools    │    │                  │
└─────────────────┘    │                  │    ┌─────────────────┐
                       │                  │◄──►│  MCP Server     │
                       │                  │    │  (Earthquake    │
                       └──────────────────┘    │   Data API)     │
                                               └─────────────────┘
```

## API Endpoints

- `GET /api/status` - System status and configuration
- `GET /api/ollama/models` - List available Ollama models
- `POST /api/ollama/chat` - Chat completion with Ollama
- `POST /api/mcp/connect` - Connect to MCP server
- `GET /api/mcp/:id/tools` - List MCP tools
- `POST /api/mcp/:id/call` - Call MCP tool
- `DELETE /api/mcp/:id` - Disconnect from MCP server

## Integration with MCP Server

This web client is designed to work with the MCP Dante Server component. Ensure the MCP server is running and accessible via the Docker network before starting the web client.

## Troubleshooting

### Common Issues

1. **Ollama Connection Failed**: Verify Ollama is running at the specified URL
2. **MCP Connection Failed**: Ensure MCP server container is running and accessible
3. **No Models Available**: Check Ollama installation and model availability
4. **Docker Socket Permission**: Ensure proper Docker socket permissions for container communication
