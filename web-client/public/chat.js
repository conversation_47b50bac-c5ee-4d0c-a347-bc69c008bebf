// MCP Dante AI Chat Client
class MCPDanteAIChat {
    constructor() {
        this.socket = io();
        this.sessionId = this.generateSessionId();
        this.mcpConnectionId = null;
        this.selectedModel = null;
        this.baseUrl = window.location.origin;

        this.initializeEventListeners();
        this.initializeChat();
        this.loadModels();
        this.checkSystemStatus();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    initializeEventListeners() {
        // Chat functionality
        document.getElementById('sendButton').addEventListener('click', () => this.sendMessage());
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // Model selection
        document.getElementById('modelSelect').addEventListener('change', (e) => {
            this.selectedModel = e.target.value;
            this.updateChatInterface();
        });

        // MCP connection
        document.getElementById('connectMcpButton').addEventListener('click', () => this.connectMCP());
        document.getElementById('disconnectMcpButton').addEventListener('click', () => this.disconnectMCP());
        document.getElementById('testMcpButton').addEventListener('click', () => this.testMCPIntegration());

        // Custom MCP form
        document.getElementById('customMcpForm').addEventListener('submit', (e) => this.handleCustomMcpQuery(e));

        // Socket events
        this.socket.on('message-response', (data) => this.handleMessageResponse(data));
        this.socket.on('error', (data) => this.handleError(data));
        this.socket.on('mcp-tool-used', (data) => this.handleMCPToolUsed(data));
    }

    initializeChat() {
        // Join chat session
        this.socket.emit('join-chat', { sessionId: this.sessionId });

        // Update session info
        document.getElementById('sessionId').textContent = this.sessionId;
    }

    async loadModels() {
        try {
            const response = await fetch(`${this.baseUrl}/api/ollama/models`);
            const data = await response.json();

            if (data.success && data.models.length > 0) {
                this.populateModelSelect(data.models);
                this.updateOllamaStatus('connected', 'Connected');
            } else {
                this.updateOllamaStatus('disconnected', 'No models available');
            }
        } catch (error) {
            console.error('Error loading models:', error);
            this.updateOllamaStatus('disconnected', 'Connection failed');
        }
    }

    populateModelSelect(models) {
        const select = document.getElementById('modelSelect');
        select.innerHTML = '<option value="">Select a model...</option>';

        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = `${model.name} (${this.formatSize(model.size)})`;
            select.appendChild(option);
        });
    }

    formatSize(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 B';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    async checkSystemStatus() {
        try {
            const response = await fetch(`${this.baseUrl}/api/status`);
            const data = await response.json();

            document.getElementById('ollamaUrl').textContent = data.ollama_url || 'Unknown';
            document.getElementById('mcpServerHost').textContent = data.mcp_server_host || 'Unknown';
        } catch (error) {
            console.error('Error checking system status:', error);
        }
    }

    updateChatInterface() {
        const hasModel = this.selectedModel && this.selectedModel !== '';
        document.getElementById('messageInput').disabled = !hasModel;
        document.getElementById('sendButton').disabled = !hasModel;

        if (hasModel) {
            document.getElementById('messageInput').placeholder =
                `Ask ${this.selectedModel} about earthquakes, seismic data, or use MCP tools...`;
        }
    }

    updateOllamaStatus(status, text) {
        const indicator = document.getElementById('ollamaStatus');
        const textElement = document.getElementById('ollamaStatusText');

        indicator.className = `status-indicator status-${status}`;
        textElement.textContent = text;
    }

    updateMcpStatus(status, text) {
        const indicator = document.getElementById('mcpStatus');
        const textElement = document.getElementById('mcpStatusText');
        const connectBtn = document.getElementById('connectMcpButton');
        const disconnectBtn = document.getElementById('disconnectMcpButton');
        const toolsSection = document.getElementById('mcpToolsSection');

        indicator.className = `status-indicator status-${status}`;
        textElement.textContent = text;

        const testBtn = document.getElementById('testMcpButton');

        if (status === 'connected') {
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
            testBtn.disabled = false;
            toolsSection.style.display = 'block';
        } else {
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            testBtn.disabled = true;
            toolsSection.style.display = 'none';
        }
    }

    async connectMCP() {
        this.updateMcpStatus('connecting', 'Connecting...');

        try {
            const response = await fetch(`${this.baseUrl}/api/mcp/connect`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();

            if (data.success) {
                this.mcpConnectionId = data.connectionId;
                this.updateMcpStatus('connected', 'Connected');
                this.addSystemMessage('Connected to MCP server. You can now use earthquake data tools!');

                // Link MCP connection to chat session
                console.log(`🔗 Linking session ${this.sessionId} to MCP connection ${this.mcpConnectionId}`);
                this.socket.emit('set-mcp-connection', {
                    sessionId: this.sessionId,
                    connectionId: this.mcpConnectionId
                });

                // Add a small delay to ensure linking is complete
                setTimeout(() => {
                    this.addSystemMessage('🤖 AI can now access real-time earthquake data! Try asking: "How many earthquakes happened today?" or "Show me recent earthquakes in Italy"');
                }, 500);
            } else {
                throw new Error(data.error || 'Connection failed');
            }
        } catch (error) {
            this.updateMcpStatus('disconnected', 'Connection failed');
            this.addSystemMessage(`MCP connection failed: ${error.message}`);
        }
    }

    async disconnectMCP() {
        if (!this.mcpConnectionId) return;

        try {
            await fetch(`${this.baseUrl}/api/mcp/${this.mcpConnectionId}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Error disconnecting MCP:', error);
        }

        this.mcpConnectionId = null;
        this.updateMcpStatus('disconnected', 'Disconnected');
        this.addSystemMessage('Disconnected from MCP server.');
    }

    async testMCPIntegration() {
        this.addSystemMessage('🧪 Testing MCP integration...');

        // Test 1: Check routing logic
        try {
            const testQuery = "How many earthquakes happened today?";
            const response = await fetch(`${this.baseUrl}/api/debug/test-routing`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: testQuery })
            });

            const data = await response.json();
            this.addSystemMessage(`🔍 Routing test: "${testQuery}" → shouldUseMCP: ${data.shouldUseMCP}, tool: ${data.selectedTool}`);

        } catch (error) {
            this.addSystemMessage(`❌ Routing test failed: ${error.message}`);
        }

        // Test 2: Direct MCP tool call
        if (this.mcpConnectionId) {
            try {
                const result = await this.useMcpTool('dante_status', {});
                this.addSystemMessage('✅ Direct MCP tool call successful');
            } catch (error) {
                this.addSystemMessage(`❌ Direct MCP tool call failed: ${error.message}`);
            }
        } else {
            this.addSystemMessage('❌ No MCP connection available for testing');
        }

        // Test 3: Send a test earthquake query
        if (this.selectedModel) {
            this.addSystemMessage('🤖 Sending test earthquake query to AI...');
            const testInput = document.getElementById('messageInput');
            const originalValue = testInput.value;
            testInput.value = "How many earthquakes happened today?";
            this.sendMessage();
            testInput.value = originalValue;
        } else {
            this.addSystemMessage('❌ No AI model selected for testing');
        }
    }

    sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();

        if (!message || !this.selectedModel) return;

        // Add user message to chat
        this.addMessage(message, 'user');

        // Clear input and show typing indicator
        input.value = '';
        this.showTypingIndicator();

        // Check if this is an earthquake-related query and we have MCP connection
        if (this.isEarthquakeQuery(message) && this.mcpConnectionId) {
            this.addSystemMessage('🔍 Detected earthquake query - using MCP tools for real-time data...');
            this.handleEarthquakeQuery(message);
        } else {
            // Send to AI via socket
            this.socket.emit('send-message', {
                sessionId: this.sessionId,
                message: message,
                model: this.selectedModel
            });
        }
    }

    isEarthquakeQuery(message) {
        const earthquakeKeywords = [
            'earthquake', 'earthquakes', 'seismic', 'tremor', 'quake', 'quakes',
            'magnitude', 'richter', 'epicenter', 'fault', 'tectonic',
            'aftershock', 'foreshock', 'tsunami', 'geological', 'geology',
            'today', 'recent', 'latest', 'current', 'now', 'this week',
            'italy', 'italian', 'mediterranean', 'europe', 'ingv'
        ];

        const lowerMessage = message.toLowerCase();
        return earthquakeKeywords.some(keyword => lowerMessage.includes(keyword));
    }

    async handleEarthquakeQuery(message) {
        try {
            // Determine which tool to use based on the query
            const toolName = this.selectEarthquakeTool(message);
            const params = this.extractEarthquakeParams(message);

            this.addSystemMessage(`🔧 Using MCP tool: ${toolName} with params: ${JSON.stringify(params)}`);

            // Call the MCP tool directly
            const result = await this.useMcpToolDirect(toolName, params);

            if (result) {
                // Format the earthquake data for display
                const formattedResponse = this.formatEarthquakeData(result, message);
                this.hideTypingIndicator();
                this.addMessage(formattedResponse, 'assistant', true);
            } else {
                throw new Error('No data received from MCP tool');
            }
        } catch (error) {
            console.error('Error handling earthquake query:', error);
            this.hideTypingIndicator();
            this.addSystemMessage(`❌ Error getting earthquake data: ${error.message}`);

            // Fallback to regular AI response
            this.socket.emit('send-message', {
                sessionId: this.sessionId,
                message: message,
                model: this.selectedModel
            });
        }
    }

    selectEarthquakeTool(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('status') || lowerMessage.includes('system') || lowerMessage.includes('api')) {
            return 'dante_status';
        } else if (lowerMessage.includes('detailed') || lowerMessage.includes('full') || lowerMessage.includes('complete')) {
            return 'dante_get_all_data';
        } else if (lowerMessage.includes('origin') || lowerMessage.includes('location')) {
            return 'dante_get_origins';
        } else if (lowerMessage.includes('magnitude')) {
            return 'dante_get_magnitudes';
        } else {
            return 'dante_get_events_group'; // Default for general earthquake queries
        }
    }

    extractEarthquakeParams(message) {
        const lowerMessage = message.toLowerCase();
        const params = {};

        // Extract time-based parameters
        if (lowerMessage.includes('today')) {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            params.starttime = yesterday.toISOString();
            params.endtime = today.toISOString();
        } else if (lowerMessage.includes('recent') || lowerMessage.includes('latest')) {
            const now = new Date();
            const weekAgo = new Date(now);
            weekAgo.setDate(weekAgo.getDate() - 7);
            params.starttime = weekAgo.toISOString();
            params.endtime = now.toISOString();
        } else if (lowerMessage.includes('this week')) {
            const now = new Date();
            const weekAgo = new Date(now);
            weekAgo.setDate(weekAgo.getDate() - 7);
            params.starttime = weekAgo.toISOString();
            params.endtime = now.toISOString();
        }

        // Extract location parameters for Italy
        if (lowerMessage.includes('italy') || lowerMessage.includes('italian')) {
            params.minlat = 35.0;
            params.maxlat = 47.0;
            params.minlon = 6.0;
            params.maxlon = 19.0;
        }

        // Set default limit
        params.limit = 20;

        return params;
    }

    async useMcpToolDirect(toolName, args = {}) {
        if (!this.mcpConnectionId) {
            throw new Error('No MCP connection available');
        }

        const response = await fetch(`${this.baseUrl}/api/mcp/${this.mcpConnectionId}/call`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                toolName: toolName,
                arguments: args
            })
        });

        const data = await response.json();

        if (data.success) {
            return data.result;
        } else {
            throw new Error(data.error || 'Tool call failed');
        }
    }

    formatEarthquakeData(result, originalQuery) {
        try {
            // Parse the result content
            let earthquakeData;
            if (result.content && result.content[0] && result.content[0].text) {
                earthquakeData = JSON.parse(result.content[0].text);
            } else {
                earthquakeData = result;
            }

            // Format based on the type of data
            if (earthquakeData.events && Array.isArray(earthquakeData.events)) {
                const events = earthquakeData.events;
                const count = events.length;

                let response = `Found ${count} earthquake events:\n\n`;

                events.slice(0, 10).forEach((event, index) => {
                    const mag = event.magnitude || 'Unknown';
                    const location = event.location || event.place || 'Unknown location';
                    const time = event.time ? new Date(event.time).toLocaleString() : 'Unknown time';

                    response += `${index + 1}. Magnitude ${mag} - ${location}\n   Time: ${time}\n\n`;
                });

                if (count > 10) {
                    response += `... and ${count - 10} more events.`;
                }

                return response;
            } else if (earthquakeData.status) {
                return `API Status: ${earthquakeData.status}\nMessage: ${earthquakeData.message || 'System operational'}`;
            } else {
                return `Earthquake data retrieved:\n${JSON.stringify(earthquakeData, null, 2)}`;
            }
        } catch (error) {
            console.error('Error formatting earthquake data:', error);
            return `Raw earthquake data:\n${JSON.stringify(result, null, 2)}`;
        }
    }

    handleMessageResponse(data) {
        this.hideTypingIndicator();
        this.addMessage(data.assistant, 'assistant', data.usedMCP);
    }

    handleError(data) {
        this.hideTypingIndicator();
        this.addSystemMessage(`Error: ${data.message}`);
    }

    handleMCPToolUsed(data) {
        this.addSystemMessage(`🔧 Used MCP tool: ${data.toolName} with parameters: ${JSON.stringify(data.params)}`);
    }

    addMessage(content, type, usedMCP = false) {
        const chatContainer = document.getElementById('chatContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        if (type === 'user') {
            messageDiv.innerHTML = `<strong>You:</strong> ${this.escapeHtml(content)}`;
        } else if (type === 'assistant') {
            const mcpBadge = usedMCP ? '<span class="badge bg-success ms-2" title="Response includes real-time earthquake data">📊 Real Data</span>' : '';
            messageDiv.innerHTML = `<strong>AI:</strong>${mcpBadge} ${this.formatAIResponse(content)}`;
        }

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    addSystemMessage(content) {
        const chatContainer = document.getElementById('chatContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system';
        messageDiv.textContent = content;

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    formatAIResponse(content) {
        // Basic formatting for AI responses
        return this.escapeHtml(content)
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'block';
    }

    hideTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'none';
    }

    async useMcpTool(toolName, args = {}) {
        if (!this.mcpConnectionId) {
            this.addSystemMessage('Please connect to MCP server first.');
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/api/mcp/${this.mcpConnectionId}/call`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    toolName: toolName,
                    arguments: args
                })
            });

            const data = await response.json();

            if (data.success) {
                const result = data.result.content?.[0]?.text || JSON.stringify(data.result);
                this.addSystemMessage(`MCP Tool "${toolName}" executed successfully.`);
                this.addMessage(`Tool result: ${result}`, 'assistant');
            } else {
                throw new Error(data.error || 'Tool call failed');
            }
        } catch (error) {
            this.addSystemMessage(`Error calling MCP tool "${toolName}": ${error.message}`);
        }
    }

    handleCustomMcpQuery(event) {
        event.preventDefault();

        const args = {};
        const minMag = document.getElementById('minMag').value;
        const maxMag = document.getElementById('maxMag').value;
        const minLat = document.getElementById('minLat').value;
        const maxLat = document.getElementById('maxLat').value;
        const minLon = document.getElementById('minLon').value;
        const maxLon = document.getElementById('maxLon').value;
        const toolName = document.getElementById('toolSelect').value;

        if (minMag) args.minmag = parseFloat(minMag);
        if (maxMag) args.maxmag = parseFloat(maxMag);
        if (minLat) args.minlat = parseFloat(minLat);
        if (maxLat) args.maxlat = parseFloat(maxLat);
        if (minLon) args.minlon = parseFloat(minLon);
        if (maxLon) args.maxlon = parseFloat(maxLon);

        this.useMcpTool(toolName, args);
    }
}

// Global function for tool buttons
function useMcpTool(toolName, args = {}) {
    window.mcpChat.useMcpTool(toolName, args);
}

// Global function for example queries
function setExampleQuery(query) {
    const messageInput = document.getElementById('messageInput');
    messageInput.value = query;
    messageInput.focus();
}

// Initialize the chat client when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.mcpChat = new MCPDanteAIChat();
});
