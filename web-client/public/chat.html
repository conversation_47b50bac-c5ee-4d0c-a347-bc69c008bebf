<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Dante AI Chat - Earthquake Data Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="/socket.io/socket.io.js"></script>
    <style>
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            max-width: 80%;
        }

        .message.user {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background-color: #e9ecef;
            color: #333;
            margin-right: auto;
        }

        .message.system {
            background-color: #fff3cd;
            color: #856404;
            text-align: center;
            margin: 0 auto;
            font-style: italic;
        }

        .typing-indicator {
            display: none;
            padding: 0.5rem;
            font-style: italic;
            color: #6c757d;
        }

        .model-selector {
            margin-bottom: 1rem;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-connected {
            background-color: #28a745;
        }

        .status-connecting {
            background-color: #ffc107;
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .mcp-tools {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }

        .tool-button {
            margin: 0.25rem;
        }

        .message.assistant .badge {
            font-size: 0.7rem;
            vertical-align: top;
        }

        .example-queries .btn {
            font-size: 0.8rem;
            margin: 0.1rem;
        }
    </style>
</head>

<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-robot"></i> MCP Dante AI Chat
                    <small class="text-muted d-block">AI-Powered Earthquake Data Analysis</small>
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- Chat Interface -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-comments"></i> Chat with AI</h5>
                            <div class="connection-status">
                                <div id="ollamaStatus" class="status-indicator status-disconnected"></div>
                                <span id="ollamaStatusText">Checking Ollama...</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Model Selection -->
                        <div class="model-selector">
                            <label for="modelSelect" class="form-label">AI Model:</label>
                            <select id="modelSelect" class="form-select">
                                <option value="">Loading models...</option>
                            </select>
                        </div>

                        <!-- Chat Messages -->
                        <div id="chatContainer" class="chat-container">
                            <div class="message system">
                                🌍 Welcome! I'm your AI assistant for earthquake data analysis powered by real-time INGV
                                data.
                                <br><br>
                                <strong>How it works:</strong>
                                <br>1. Select an AI model above
                                <br>2. Connect to MCP server (right panel) for real-time earthquake data
                                <br>3. Ask me about earthquakes - I'll automatically fetch current data when needed!
                                <br><br>
                                <strong>Example questions:</strong> "How many earthquakes happened today?", "Show me
                                recent earthquakes in Italy", "What's the system status?"
                            </div>
                        </div>

                        <div id="typingIndicator" class="typing-indicator">
                            <i class="fas fa-spinner fa-spin"></i> AI is thinking...
                        </div>

                        <!-- Example Prompts -->
                        <div class="mt-3">
                            <small class="text-muted">Try these earthquake queries:</small>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                <button class="btn btn-outline-secondary btn-sm"
                                    onclick="setExampleQuery('How many earthquakes happened today?')">
                                    Today's earthquakes
                                </button>
                                <button class="btn btn-outline-secondary btn-sm"
                                    onclick="setExampleQuery('Show me recent earthquakes in Italy with magnitude greater than 3.0')">
                                    Italy M3.0+
                                </button>
                                <button class="btn btn-outline-secondary btn-sm"
                                    onclick="setExampleQuery('What is the status of the earthquake monitoring system?')">
                                    System status
                                </button>
                                <button class="btn btn-outline-secondary btn-sm"
                                    onclick="setExampleQuery('Find earthquakes this week in the Mediterranean')">
                                    Mediterranean
                                </button>
                            </div>
                        </div>

                        <!-- Message Input -->
                        <div class="input-group mt-3">
                            <input type="text" id="messageInput" class="form-control"
                                placeholder="Ask about earthquakes, seismic data, or use MCP tools..." disabled>
                            <button id="sendButton" class="btn btn-primary" type="button" disabled>
                                <i class="fas fa-paper-plane"></i> Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MCP Tools Panel -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-tools"></i> MCP Tools</h5>
                            <div class="connection-status">
                                <div id="mcpStatus" class="status-indicator status-disconnected"></div>
                                <span id="mcpStatusText">Disconnected</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 mb-3">
                            <button id="connectMcpButton" class="btn btn-success">
                                <i class="fas fa-plug"></i> Connect to MCP Server
                            </button>
                            <button id="disconnectMcpButton" class="btn btn-danger" disabled>
                                <i class="fas fa-unplug"></i> Disconnect
                            </button>
                            <button id="testMcpButton" class="btn btn-warning" disabled>
                                <i class="fas fa-vial"></i> Test MCP Integration
                            </button>
                        </div>

                        <div id="mcpToolsSection" style="display: none;">
                            <h6>Available Tools:</h6>
                            <div class="mcp-tools">
                                <button class="btn btn-outline-primary btn-sm tool-button"
                                    onclick="useMcpTool('dante_status')">
                                    <i class="fas fa-server"></i> API Status
                                </button>
                                <button class="btn btn-outline-primary btn-sm tool-button"
                                    onclick="useMcpTool('dante_get_events_group', {minmag: 3.0, limit: 10})">
                                    <i class="fas fa-list"></i> Recent Events
                                </button>
                                <button class="btn btn-outline-primary btn-sm tool-button"
                                    onclick="useMcpTool('dante_get_events', {minlat: 35, maxlat: 47, minlon: 6, maxlon: 19, minmag: 2.0, limit: 15})">
                                    <i class="fas fa-map"></i> Italy Events
                                </button>
                            </div>

                            <div class="mt-3">
                                <h6>Custom Query:</h6>
                                <form id="customMcpForm">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="minMag"
                                                placeholder="Min Mag" step="0.1">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="maxMag"
                                                placeholder="Max Mag" step="0.1">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="minLat"
                                                placeholder="Min Lat" step="0.001">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="maxLat"
                                                placeholder="Max Lat" step="0.001">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="minLon"
                                                placeholder="Min Lon" step="0.001">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="maxLon"
                                                placeholder="Max Lon" step="0.001">
                                        </div>
                                        <div class="col-12">
                                            <select id="toolSelect" class="form-select form-select-sm">
                                                <option value="dante_get_events_group">Events Group</option>
                                                <option value="dante_get_events">Events</option>
                                                <option value="dante_get_origins">Origins</option>
                                                <option value="dante_get_magnitudes">Magnitudes</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                                <i class="fas fa-search"></i> Query Data
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> System Info</h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <div><strong>Ollama:</strong> <span id="ollamaUrl">-</span></div>
                            <div><strong>MCP Server:</strong> <span id="mcpServerHost">-</span></div>
                            <div><strong>Session:</strong> <span id="sessionId">-</span></div>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chat.js"></script>
</body>

</html>