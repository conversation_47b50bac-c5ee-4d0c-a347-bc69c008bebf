#!/usr/bin/env python3
"""
Example usage of the MCP Dante Server

This script demonstrates how to interact with the MCP server
to get earthquake data from the Dante API.
"""

import json
import subprocess
import sys
import time
from datetime import datetime, timed<PERSON><PERSON>


def run_mcp_tool(tool_name, arguments=None):
    """
    Simulate calling an MCP tool by directly calling the handler functions
    """
    if arguments is None:
        arguments = {}
    
    # Import the MCP server functions
    sys.path.append('src')
    import asyncio
    from mcp_dante_server import handle_call_tool
    
    async def call_tool():
        try:
            result = await handle_call_tool(tool_name, arguments)
            return result[0].text if result else "No result"
        except Exception as e:
            return f"Error: {str(e)}"
    
    return asyncio.run(call_tool())


def main():
    """Demonstrate MCP Dante Server usage"""
    print("=== MCP Dante Server Usage Example ===\n")
    
    # 1. Check API status
    print("1. 🔍 Checking Dante API status...")
    status_result = run_mcp_tool("dante_status")
    print("✅ Status check completed!")
    print(f"Result: {status_result[:200]}...\n")
    
    # 2. Get recent earthquakes with magnitude > 3.0
    print("2. 🌍 Getting recent earthquakes (magnitude > 3.0)...")
    events_result = run_mcp_tool("dante_get_events_group", {
        "minmag": 3.0,
        "limit": 5
    })
    print("✅ Events retrieved!")
    print(f"Result: {events_result[:300]}...\n")
    
    # 3. Get earthquakes in Italy region
    print("3. 🇮🇹 Getting earthquakes in Italy region...")
    italy_events = run_mcp_tool("dante_get_events", {
        "minlat": 35.0,
        "maxlat": 47.0,
        "minlon": 6.0,
        "maxlon": 19.0,
        "minmag": 2.0,
        "limit": 10
    })
    print("✅ Italy earthquakes retrieved!")
    print(f"Result: {italy_events[:300]}...\n")
    
    # 4. Get earthquake origins data
    print("4. 📍 Getting earthquake origins...")
    origins_result = run_mcp_tool("dante_get_origins", {
        "limit": 3,
        "minmag": 4.0
    })
    print("✅ Origins retrieved!")
    print(f"Result: {origins_result[:300]}...\n")
    
    # 5. Get magnitude data
    print("5. 📊 Getting magnitude data...")
    magnitudes_result = run_mcp_tool("dante_get_magnitudes", {
        "limit": 3,
        "minmag": 3.5
    })
    print("✅ Magnitudes retrieved!")
    print(f"Result: {magnitudes_result[:300]}...\n")
    
    print("🎉 MCP Dante Server demonstration completed!")
    print("\nTo use this server with an MCP client:")
    print("1. Start the server: docker-compose up mcp-dante")
    print("2. Connect your MCP client to the server's stdio interface")
    print("3. Use the available tools to query earthquake data")


if __name__ == "__main__":
    main()
