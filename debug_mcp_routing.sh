#!/bin/bash

# Debug script for MCP routing
# This script tests the MCP routing logic and connection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test routing logic
test_routing_logic() {
    print_status "Testing MCP routing logic..."
    
    local test_queries=(
        "How many earthquakes happened today?"
        "Show me recent earthquakes in Italy"
        "What is the weather like?"
        "Tell me about seismic activity"
        "What's the status of the earthquake monitoring system?"
    )
    
    for query in "${test_queries[@]}"; do
        print_status "Testing query: '$query'"
        
        local response=$(curl -s -X POST http://localhost:3000/api/debug/test-routing \
            -H "Content-Type: application/json" \
            -d "{\"message\": \"$query\"}")
        
        if [ $? -eq 0 ]; then
            local should_use=$(echo "$response" | jq -r '.shouldUseMCP' 2>/dev/null)
            local tool=$(echo "$response" | jq -r '.selectedTool' 2>/dev/null)
            
            if [ "$should_use" = "true" ]; then
                print_success "✅ Should use MCP (tool: $tool)"
            else
                print_warning "❌ Should NOT use MCP"
            fi
        else
            print_error "Failed to test query"
        fi
        echo ""
    done
}

# Test MCP connection
test_mcp_connection() {
    print_status "Testing MCP connection..."
    
    # Try to connect
    local connect_response=$(curl -s -X POST http://localhost:3000/api/mcp/connect \
        -H "Content-Type: application/json")
    
    local connection_id=$(echo "$connect_response" | jq -r '.connectionId' 2>/dev/null)
    
    if [ "$connection_id" != "null" ] && [ "$connection_id" != "" ]; then
        print_success "MCP connection established: $connection_id"
        
        # Test tool call
        print_status "Testing MCP tool call..."
        local tool_response=$(curl -s -X POST "http://localhost:3000/api/mcp/$connection_id/call" \
            -H "Content-Type: application/json" \
            -d '{"toolName": "dante_status", "arguments": {}}')
        
        if echo "$tool_response" | jq -e '.success' >/dev/null 2>&1; then
            print_success "MCP tool call successful"
        else
            print_error "MCP tool call failed"
            echo "Response: $tool_response"
        fi
        
        # Clean up
        curl -s -X DELETE "http://localhost:3000/api/mcp/$connection_id" >/dev/null
        
    else
        print_error "Failed to establish MCP connection"
        echo "Response: $connect_response"
    fi
}

# Check container logs
check_logs() {
    print_status "Checking recent container logs..."
    
    echo ""
    print_status "=== MCP Server Logs (last 20 lines) ==="
    docker logs --tail 20 mcp-dante-server 2>/dev/null || print_warning "MCP server container not found"
    
    echo ""
    print_status "=== Web Client Logs (last 20 lines) ==="
    docker logs --tail 20 mcp-dante-web-client 2>/dev/null || print_warning "Web client container not found"
}

# Test web client status
test_web_status() {
    print_status "Testing web client status..."
    
    local response=$(curl -s http://localhost:3000/api/status)
    if [ $? -eq 0 ]; then
        print_success "Web client is responding"
        echo "Status: $response" | jq '.' 2>/dev/null || echo "Status: $response"
    else
        print_error "Web client is not responding"
    fi
}

# Main execution
main() {
    echo "🔍 MCP Routing Debug Tool"
    echo "========================="
    echo ""
    
    test_web_status
    echo ""
    
    test_routing_logic
    echo ""
    
    test_mcp_connection
    echo ""
    
    check_logs
    echo ""
    
    print_status "Debug complete!"
    echo ""
    echo "💡 To manually test:"
    echo "   1. Open http://localhost:3000/chat.html"
    echo "   2. Check browser console for debug messages"
    echo "   3. Check container logs: docker logs -f mcp-dante-web-client"
}

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
    print_warning "jq is not installed. Install with: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo ""
fi

main "$@"
