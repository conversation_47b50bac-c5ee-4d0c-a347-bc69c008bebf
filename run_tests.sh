#!/bin/bash

echo "=== MCP Dante Server Test Suite ==="
echo

# Build the Docker container
echo "🔨 Building Docker container..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "✅ Docker build successful!"
echo

# Test the Dante API client directly
echo "🧪 Testing Dante API client..."
docker-compose run --rm mcp-dante python src/test_dante_client.py

if [ $? -ne 0 ]; then
    echo "❌ API client test failed"
    exit 1
fi

echo
echo "🧪 Testing MCP server..."
docker-compose run --rm mcp-dante python src/test_mcp_server.py

if [ $? -ne 0 ]; then
    echo "❌ MCP server test failed"
    exit 1
fi

echo
echo "🧪 Running example usage..."
docker-compose run --rm mcp-dante python src/example_usage.py

if [ $? -ne 0 ]; then
    echo "❌ Example usage failed"
    exit 1
fi

echo
echo "=== ✅ All Tests Passed! ==="
echo
echo "🎉 Your MCP Dante Server is ready to use!"
echo
echo "To run the MCP server:"
echo "  docker-compose up mcp-dante"
echo
echo "To run in background:"
echo "  docker-compose up -d mcp-dante"
echo
echo "To stop:"
echo "  docker-compose down"
echo
echo "Available tools:"
echo "  - dante_login: Authenticate with API"
echo "  - dante_status: Check API status"
echo "  - dante_get_events_group: Get clustered events"
echo "  - dante_get_events: Get events from same instance"
echo "  - dante_get_origins: Get earthquake origins"
echo "  - dante_get_magnitudes: Get magnitude data"
echo "  - dante_get_all_data: Get comprehensive data"
