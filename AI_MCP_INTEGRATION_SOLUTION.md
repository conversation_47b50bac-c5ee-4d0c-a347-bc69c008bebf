# AI-MCP Integration Solution

## Problem Solved

**Issue**: When asking Ollama AI questions like "how many earthquakes happened today", the AI was responding with general knowledge instead of using the available MCP earthquake data tools.

**Root Cause**: The AI model had no mechanism to automatically detect earthquake-related queries and route them through the MCP tools to get real-time data.

## Solution Implemented

### 🧠 Smart Query Routing System

I've implemented an intelligent routing system that:

1. **Automatically detects earthquake-related queries** using keyword analysis
2. **Routes appropriate questions through MCP tools** to get real-time data
3. **Enhances AI context** with actual earthquake data before generating responses
4. **Falls back to general AI** for non-earthquake questions

### 🔧 Key Components Added

#### 1. MCPToolRouter Class (`web-client/server.js`)

```javascript
class MCPToolRouter {
    constructor() {
        this.earthquakeKeywords = [
            'earthquake', 'earthquakes', 'seismic', 'tremor', 'quake',
            'magnitude', 'richter', 'epicenter', 'today', 'recent',
            'italy', 'mediterranean', 'ingv', ...
        ];
    }

    shouldUseMCP(message) {
        // Detects if query is earthquake-related
    }

    extractParameters(message) {
        // Extracts time, location, magnitude filters from natural language
    }

    selectTool(message) {
        // Chooses appropriate MCP tool based on query type
    }
}
```

#### 2. Enhanced Chat Message Handler

The chat handler now:
- Analyzes incoming messages for earthquake keywords
- Automatically calls appropriate MCP tools when detected
- Enhances the AI prompt with real earthquake data
- Provides context-aware responses

#### 3. Real-time Data Integration

When you ask "How many earthquakes happened today?", the system:

1. **Detects** earthquake-related keywords
2. **Extracts** time parameters ("today" → last 24 hours)
3. **Calls** `dante_get_events_group` with appropriate filters
4. **Enhances** AI prompt with real data:
   ```
   User question: "How many earthquakes happened today?"
   
   REAL-TIME EARTHQUAKE DATA (from INGV Dante API):
   Tool used: dante_get_events_group
   Parameters: {"starttime": "2024-01-15T10:00:00Z", "endtime": "2024-01-16T10:00:00Z", "limit": 20}
   Data: {"data": [{"id": 123, "magnitude": 3.2, "location": "Central Italy", ...}]}
   
   Please analyze this real earthquake data and provide a comprehensive answer...
   ```
5. **AI responds** with specific, data-driven answers

### 🎯 Smart Parameter Extraction

The system intelligently extracts parameters from natural language:

- **Time**: "today", "recent", "this week" → date ranges
- **Location**: "Italy", "Mediterranean" → geographic coordinates
- **Magnitude**: "magnitude greater than 4.0" → minmag parameter
- **Tool Selection**: "status" → dante_status, "detailed" → dante_get_all_data

### 🔗 Session Linking

Chat sessions are now linked to MCP connections:
- When you connect to MCP server, it's automatically linked to your chat session
- AI can access real-time earthquake data throughout the conversation
- Visual indicators show when real data is being used

## How to Use

### 1. Start the System
```bash
./setup.sh setup && ./setup.sh build && ./setup.sh start
```

### 2. Access Chat Interface
Open http://localhost:3000/chat.html

### 3. Setup AI Chat
1. Select an AI model from the dropdown
2. Click "Connect to MCP Server" 
3. You'll see: "🤖 AI can now access real-time earthquake data!"

### 4. Ask Earthquake Questions
Try these examples:
- "How many earthquakes happened today?"
- "Show me recent earthquakes in Italy with magnitude greater than 3.0"
- "What is the status of the earthquake monitoring system?"
- "Find earthquakes this week in the Mediterranean"

### 5. Visual Feedback
- Messages with real data show a "📊 Real Data" badge
- System messages show which MCP tools were used
- Example query buttons for quick testing

## Technical Details

### Keyword Detection
The system recognizes earthquake-related terms:
- **Seismic terms**: earthquake, seismic, tremor, quake, magnitude, richter
- **Time terms**: today, recent, latest, current, this week
- **Location terms**: Italy, Mediterranean, Europe
- **System terms**: status, API, monitoring

### Parameter Extraction Patterns
- **Time ranges**: "today" → last 24 hours, "recent" → last 7 days
- **Magnitude filters**: "magnitude greater than 4.0" → minmag: 4.0
- **Geographic bounds**: "Italy" → lat/lon bounding box

### Tool Selection Logic
- **dante_status**: queries about "status", "system", "API"
- **dante_get_all_data**: queries requesting "detailed", "full", "complete" data
- **dante_get_origins**: queries about "origin", "location"
- **dante_get_magnitudes**: queries specifically about "magnitude"
- **dante_get_events_group**: default for general earthquake queries

## Testing

### Run Integration Tests
```bash
./setup.sh test-integration
```

This tests:
- Container status
- Web client health
- Ollama connectivity
- MCP connection
- Query routing logic

### Manual Testing
1. Ask: "How many earthquakes happened today?"
2. Look for:
   - "🔧 Used MCP tool: dante_get_events_group" system message
   - "📊 Real Data" badge on AI response
   - Specific earthquake counts and details in response

## Benefits

✅ **Automatic Data Integration**: No manual tool selection needed
✅ **Natural Language Processing**: Ask questions naturally
✅ **Real-time Data**: Always current earthquake information
✅ **Context Awareness**: AI understands earthquake domain
✅ **Visual Feedback**: Clear indication when real data is used
✅ **Fallback Handling**: Graceful degradation for general questions

## Example Interaction

**User**: "How many earthquakes happened today?"

**System**: 🔧 Used MCP tool: dante_get_events_group with parameters: {"starttime": "2024-01-15T10:00:00Z", "endtime": "2024-01-16T10:00:00Z", "limit": 20}

**AI** (with 📊 Real Data badge): "Based on the real-time data from INGV's Dante API, there have been 12 earthquakes detected today. Here are the details:

- 3 earthquakes with magnitude 2.0-2.9 in Central Italy
- 2 earthquakes with magnitude 3.0-3.5 in Southern Italy  
- 7 smaller earthquakes (M < 2.0) across various regions

The largest earthquake today was M 3.4 near Naples at 14:23 UTC, at a depth of 8.2 km..."

This solution transforms the AI from a general knowledge assistant into a real-time earthquake data analyst! 🌍📊
