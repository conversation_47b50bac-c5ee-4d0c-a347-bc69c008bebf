#!/bin/bash

echo "=== MCP Dante Complete Setup Test ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
    esac
}

# Function to check if command succeeded
check_result() {
    if [ $? -eq 0 ]; then
        print_status "SUCCESS" "$1"
        return 0
    else
        print_status "ERROR" "$1"
        return 1
    fi
}

# Stop any running containers
print_status "INFO" "Stopping any running containers..."
docker-compose down > /dev/null 2>&1

# Build all containers
print_status "INFO" "Building Docker containers..."
docker-compose build
check_result "Docker build completed" || exit 1

echo
print_status "INFO" "Testing MCP Server..."

# Test 1: API connectivity
print_status "INFO" "Testing Dante API connectivity..."
docker-compose run --rm mcp-dante python src/test_dante_client.py > /dev/null 2>&1
check_result "Dante API connectivity test"

# Test 2: MCP server functionality
print_status "INFO" "Testing MCP server functionality..."
docker-compose run --rm mcp-dante python src/test_mcp_server.py > /dev/null 2>&1
check_result "MCP server functionality test"

# Test 3: Example usage
print_status "INFO" "Testing example usage..."
docker-compose run --rm mcp-dante python src/example_usage.py > /dev/null 2>&1
check_result "Example usage test"

echo
print_status "INFO" "Testing Web Client..."

# Test 4: Start web client
print_status "INFO" "Starting web client..."
docker-compose up -d mcp-dante-web > /dev/null 2>&1
check_result "Web client startup"

# Wait for web client to be ready
print_status "INFO" "Waiting for web client to be ready..."
sleep 10

# Test 5: Web client health check
print_status "INFO" "Testing web client health..."
response=$(curl -s http://localhost:3000/api/status 2>/dev/null)
if echo "$response" | grep -q '"status":"ok"'; then
    check_result "Web client health check"
else
    print_status "ERROR" "Web client health check failed"
    docker-compose logs mcp-dante-web
    exit 1
fi

# Test 6: MCP connection test
print_status "INFO" "Testing MCP connection via web client..."
connection_response=$(curl -s -X POST http://localhost:3000/api/mcp/connect 2>/dev/null)
if echo "$connection_response" | grep -q '"success":true'; then
    check_result "MCP connection via web client"
    
    # Extract connection ID for cleanup
    connection_id=$(echo "$connection_response" | grep -o '"connectionId":"[^"]*"' | cut -d'"' -f4)
    
    # Test 7: Tool listing
    print_status "INFO" "Testing tool listing via web client..."
    tools_response=$(curl -s "http://localhost:3000/api/mcp/$connection_id/tools" 2>/dev/null)
    if echo "$tools_response" | grep -q '"success":true'; then
        check_result "Tool listing via web client"
    else
        print_status "ERROR" "Tool listing via web client failed"
    fi
    
    # Test 8: Tool execution
    print_status "INFO" "Testing tool execution via web client..."
    tool_response=$(curl -s -X POST "http://localhost:3000/api/mcp/$connection_id/call" \
        -H "Content-Type: application/json" \
        -d '{"toolName":"dante_status","arguments":{}}' 2>/dev/null)
    if echo "$tool_response" | grep -q '"success":true'; then
        check_result "Tool execution via web client"
    else
        print_status "ERROR" "Tool execution via web client failed"
    fi
    
    # Cleanup connection
    curl -s -X DELETE "http://localhost:3000/api/mcp/$connection_id" > /dev/null 2>&1
else
    print_status "ERROR" "MCP connection via web client failed"
fi

echo
print_status "SUCCESS" "All tests completed!"
echo

# Show final status
print_status "INFO" "Final Setup Status:"
echo "  🌐 Web Interface: http://localhost:3000"
echo "  🐳 Docker Containers: Running"
echo "  📊 MCP Server: Ready"
echo "  🔗 API Connection: Working"
echo

print_status "INFO" "To stop the services:"
echo "  docker-compose down"
echo

print_status "INFO" "To view logs:"
echo "  docker-compose logs mcp-dante-web"
echo "  docker-compose logs mcp-dante"
echo

print_status "SUCCESS" "🎉 MCP Dante setup is complete and working!"
