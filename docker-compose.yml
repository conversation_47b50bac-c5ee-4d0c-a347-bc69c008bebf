# Main orchestration file for both MCP Server and Web Client components
# This file allows running both components together or separately

services:
  # MCP Dante Server Component
  mcp-dante-server:
    build: ./mcp-server
    container_name: mcp-dante-server
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      # Mount source code for development (optional)
      - ./mcp-server/src:/app/src:ro
    stdin_open: true
    tty: true
    command: ["python", "src/mcp_dante_server.py"]
    restart: unless-stopped
    networks:
      - mcp-network
    profiles:
      - mcp-server
      - full-stack

  # Web Chat Client Component
  mcp-dante-web-client:
    build: ./web-client
    container_name: mcp-dante-web-client
    environment:
      - NODE_ENV=production
      - OLLAMA_URL=http://cat-scenari2.int.ingv.it:11434
      - MCP_SERVER_HOST=mcp-dante-server
    ports:
      - "3000:3000"
    volumes:
      # Mount Docker socket to communicate with MCP server container
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount source code for development
      - ./web-client/public:/app/public:ro
      - ./web-client/src:/app/src:ro
    restart: unless-stopped
    networks:
      - mcp-network
    depends_on:
      - mcp-dante-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - web-client
      - full-stack

  # Test service for MCP server
  mcp-dante-test:
    build: ./mcp-server
    container_name: mcp-dante-test
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      - ./mcp-server/src:/app/src:ro
      - ./mcp-server/tests:/app/tests:ro
    depends_on:
      - mcp-dante-server
    command: ["python", "-c", "print('Test container ready')"]
    networks:
      - mcp-network
    profiles:
      - test

networks:
  mcp-network:
    driver: bridge
