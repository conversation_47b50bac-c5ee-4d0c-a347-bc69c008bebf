services:
  mcp-dante:
    build: .
    container_name: mcp-dante-server
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      # Mount source code for development (optional)
      - ./src:/app/src:ro
    stdin_open: true
    tty: true
    # For stdio communication with MCP
    command: ["python", "src/mcp_dante_server.py"]

  # Web client for MCP Dante
  mcp-dante-web:
    build: ./client
    container_name: mcp-dante-web-client
    environment:
      - NODE_ENV=production
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for development
      - ./src:/app/mcp-server/src:ro
      - ./requirements.txt:/app/mcp-server/requirements.txt:ro
    depends_on:
      - mcp-dante
    restart: unless-stopped

  # Optional: Add a test service to interact with the MCP server
  mcp-dante-test:
    build: .
    container_name: mcp-dante-test
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
    depends_on:
      - mcp-dante
    profiles:
      - test
    command: ["python", "-c", "print('Test container ready')"]
